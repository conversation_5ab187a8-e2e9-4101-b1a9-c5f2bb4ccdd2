<?php
/**
 * Script simplificado para corrigir registros antigos
 * Executa as duas operações: adicionar campos e recalcular totais
 */

// Configurações do banco de dados - ajuste conforme necessário
$host = 'localhost:33306';
$username = 'root';
$password = '';
$database = 'sis_nfe_pdv'; // Ajuste o nome do banco

?>
<!DOCTYPE html>
<html>
<head>
    <title>Correção de Registros de Caixa</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .warning { color: orange; font-weight: bold; }
        .log { max-height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; }
        button { padding: 10px 20px; margin: 5px; font-size: 14px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔧 Correção de Registros de Caixa</h1>
    <p>Este script corrige os registros de caixa antigos para incluir os totais de meios de pagamento.</p>
    
    <?php
    if (isset($_POST['action'])) {
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            if ($_POST['action'] == 'add_fields') {
                echo "<div class='step'>";
                echo "<h3>📋 Passo 1: Adicionando campos na tabela</h3>";
                
                $fields_to_add = [];
                $required_fields = ['total_stripe', 'total_CC', 'total_pix'];
                
                foreach ($required_fields as $field) {
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'tec_registers' AND COLUMN_NAME = ?");
                    $stmt->execute([$database, $field]);
                    if ($stmt->fetchColumn() == 0) {
                        $fields_to_add[] = "ADD COLUMN `$field` decimal(25,2) DEFAULT NULL";
                        echo "<span class='info'>• Campo $field será adicionado</span><br>";
                    } else {
                        echo "<span class='success'>• Campo $field já existe</span><br>";
                    }
                }
                
                if (!empty($fields_to_add)) {
                    $sql = "ALTER TABLE `tec_registers` " . implode(', ', $fields_to_add);
                    $pdo->exec($sql);
                    echo "<br><span class='success'>✓ Campos adicionados com sucesso!</span>";
                } else {
                    echo "<br><span class='info'>ℹ Todos os campos já existem.</span>";
                }
                echo "</div>";
                
            } elseif ($_POST['action'] == 'recalculate') {
                echo "<div class='step'>";
                echo "<h3>🔄 Passo 2: Recalculando totais</h3>";
                
                // Buscar registros que precisam ser atualizados
                $stmt = $pdo->query("
                    SELECT id, date, closed_at, user_id, total_stripe, total_CC, total_pix
                    FROM tec_registers 
                    WHERE status = 'close' 
                    AND closed_at IS NOT NULL
                    AND (total_stripe IS NULL OR total_CC IS NULL OR total_pix IS NULL)
                    ORDER BY date ASC
                    LIMIT 50
                ");
                
                $registers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $count = count($registers);
                
                if ($count == 0) {
                    echo "<span class='info'>ℹ Nenhum registro precisa ser atualizado.</span>";
                } else {
                    echo "<span class='info'>Processando $count registros...</span><br><br>";
                    echo "<div class='log'>";
                    
                    foreach ($registers as $register) {
                        $register_id = $register['id'];
                        $open_date = $register['date'];
                        $close_date = $register['closed_at'];
                        $user_id = $register['user_id'];
                        
                        // Calcular totais
                        $totals = [];
                        $payment_types = ['stripe' => 'Débito', 'CC' => 'Crédito', 'pix' => 'PIX', 'cash' => 'Dinheiro'];
                        
                        foreach (['stripe', 'CC', 'pix'] as $type) {
                            $stmt_total = $pdo->prepare("
                                SELECT SUM(COALESCE(amount, 0)) as total
                                FROM tec_payments 
                                WHERE date > ? AND date <= ? AND created_by = ? AND paid_by = ?
                            ");
                            $stmt_total->execute([$open_date, $close_date, $user_id, $type]);
                            $totals[$type] = $stmt_total->fetchColumn() ?: 0;
                        }
                        
                        // Atualizar registro
                        $stmt_update = $pdo->prepare("
                            UPDATE tec_registers 
                            SET total_stripe = ?, total_CC = ?, total_pix = ?
                            WHERE id = ?
                        ");
                        $stmt_update->execute([$totals['stripe'], $totals['CC'], $totals['pix'], $register_id]);
                        
                        echo "• Registro #$register_id: ";
                        echo "Débito: R$ " . number_format($totals['stripe'], 2, ',', '.') . " | ";
                        echo "Crédito: R$ " . number_format($totals['CC'], 2, ',', '.') . " | ";
                        echo "PIX: R$ " . number_format($totals['pix'], 2, ',', '.') . "<br>";
                    }
                    
                    echo "</div>";
                    echo "<br><span class='success'>✓ $count registros atualizados!</span>";
                    
                    // Verificar se há mais registros
                    $stmt_remaining = $pdo->query("
                        SELECT COUNT(*) FROM tec_registers 
                        WHERE status = 'close' AND closed_at IS NOT NULL
                        AND (total_stripe IS NULL OR total_CC IS NULL OR total_pix IS NULL)
                    ");
                    $remaining = $stmt_remaining->fetchColumn();
                    
                    if ($remaining > 0) {
                        echo "<br><span class='warning'>⚠ Ainda restam $remaining registros. Execute novamente para processar mais.</span>";
                    }
                }
                echo "</div>";
            }
            
        } catch (PDOException $e) {
            echo "<div class='error'>Erro: " . $e->getMessage() . "</div>";
        }
    }
    ?>
    
    <div class='step'>
        <h3>🚀 Executar Correção</h3>
        <p>Execute os passos na ordem:</p>
        
        <form method="post" style="display: inline;">
            <input type="hidden" name="action" value="add_fields">
            <button type="submit">1️⃣ Adicionar Campos na Tabela</button>
        </form>
        
        <form method="post" style="display: inline;">
            <input type="hidden" name="action" value="recalculate">
            <button type="submit">2️⃣ Recalcular Totais</button>
        </form>
    </div>
    
    <div class='step'>
        <h3>📊 Verificar Resultados</h3>
        <p>Após executar os passos acima, acesse:</p>
        <ul>
            <li><strong>Relatórios → Caixas</strong> para ver os totais de meios de pagamento</li>
            <li>Os valores aparecerão nas colunas: <strong>Débito</strong>, <strong>Crédito</strong>, <strong>PIX</strong></li>
        </ul>
    </div>
</body>
</html>
