<?php
// Script para fazer backup da tabela tec_registers antes das correções

require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=$PDV_HOST;dbname=$PDV_BASE;charset=utf8", $PDV_USUARIO, $PDV_SENHA);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>💾 Backup da Tabela tec_registers</h2>\n";
    echo "<p><em>Criar backup de segurança antes de executar correções</em></p>\n";
    
    $backup_table = 'tec_registers_backup_' . date('Y_m_d_H_i_s');
    
    if (isset($_POST['create_backup'])) {
        echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; margin: 10px 0;'>\n";
        echo "<h3>🔄 Criando Backup...</h3>\n";
        
        try {
            // Criar tabela de backup
            $stmt = $pdo->prepare("CREATE TABLE `$backup_table` LIKE `tec_registers`");
            $stmt->execute();
            
            echo "<p>✅ Estrutura da tabela de backup criada: <code>$backup_table</code></p>\n";
            
            // Copiar dados
            $stmt = $pdo->prepare("INSERT INTO `$backup_table` SELECT * FROM `tec_registers`");
            $stmt->execute();
            $rows_copied = $stmt->rowCount();
            
            echo "<p>✅ Dados copiados: <strong>$rows_copied</strong> registros</p>\n";
            
            // Verificar integridade
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM `$backup_table`");
            $stmt->execute();
            $backup_count = $stmt->fetchColumn();
            
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM `tec_registers`");
            $stmt->execute();
            $original_count = $stmt->fetchColumn();
            
            if ($backup_count == $original_count) {
                echo "<p style='color: green;'>✅ <strong>Backup criado com sucesso!</strong></p>\n";
                echo "<p>Tabela original: $original_count registros</p>\n";
                echo "<p>Tabela backup: $backup_count registros</p>\n";
                
                // Criar script de restauração
                $restore_script = "<?php\n";
                $restore_script .= "// Script de restauração automática\n";
                $restore_script .= "// Gerado em: " . date('d/m/Y H:i:s') . "\n\n";
                $restore_script .= "require_once 'config.php';\n\n";
                $restore_script .= "try {\n";
                $restore_script .= "    \$pdo = new PDO(\"mysql:host=\$PDV_HOST;dbname=\$PDV_BASE;charset=utf8\", \$PDV_USUARIO, \$PDV_SENHA);\n";
                $restore_script .= "    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);\n\n";
                $restore_script .= "    echo \"<h2>🔄 Restaurando Backup</h2>\\n\";\n\n";
                $restore_script .= "    // Truncar tabela original\n";
                $restore_script .= "    \$pdo->exec(\"TRUNCATE TABLE tec_registers\");\n";
                $restore_script .= "    echo \"<p>✅ Tabela original limpa</p>\\n\";\n\n";
                $restore_script .= "    // Restaurar dados do backup\n";
                $restore_script .= "    \$pdo->exec(\"INSERT INTO tec_registers SELECT * FROM $backup_table\");\n";
                $restore_script .= "    echo \"<p>✅ Dados restaurados do backup</p>\\n\";\n\n";
                $restore_script .= "    echo \"<p style='color: green;'><strong>Restauração concluída com sucesso!</strong></p>\\n\";\n\n";
                $restore_script .= "} catch (PDOException \$e) {\n";
                $restore_script .= "    echo \"<p style='color: red;'>Erro: \" . \$e->getMessage() . \"</p>\\n\";\n";
                $restore_script .= "}\n";
                $restore_script .= "?>";
                
                file_put_contents("restore_backup_$backup_table.php", $restore_script);
                
                echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #2196F3; margin: 10px 0;'>\n";
                echo "<h4>📋 Informações do Backup:</h4>\n";
                echo "<ul>\n";
                echo "<li><strong>Tabela de backup:</strong> <code>$backup_table</code></li>\n";
                echo "<li><strong>Registros copiados:</strong> $backup_count</li>\n";
                echo "<li><strong>Data/Hora:</strong> " . date('d/m/Y H:i:s') . "</li>\n";
                echo "<li><strong>Script de restauração:</strong> <code>restore_backup_$backup_table.php</code></li>\n";
                echo "</ul>\n";
                echo "</div>\n";
                
                echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>\n";
                echo "<h4>⚠️ Importante:</h4>\n";
                echo "<ul>\n";
                echo "<li>O backup foi criado na mesma base de dados</li>\n";
                echo "<li>Para restaurar, execute o arquivo: <code>restore_backup_$backup_table.php</code></li>\n";
                echo "<li>Mantenha este backup até confirmar que as correções funcionaram corretamente</li>\n";
                echo "<li>Após confirmar, você pode remover a tabela de backup para economizar espaço</li>\n";
                echo "</ul>\n";
                echo "</div>\n";
                
                echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; margin: 10px 0;'>\n";
                echo "<h4>🚀 Próximos Passos:</h4>\n";
                echo "<p>Agora você pode executar as correções com segurança:</p>\n";
                echo "<ol>\n";
                echo "<li><a href='analyze_old_data.php' target='_blank' style='color: #2196F3;'>📊 Analisar dados antigos</a></li>\n";
                echo "<li><a href='fix_old_reports.php' target='_blank' style='color: #ff9800;'>🔧 Corrigir relatórios antigos</a></li>\n";
                echo "<li><a href='fix_register_totals.php' target='_blank' style='color: #ff9800;'>🔧 Corrigir totais recentes</a></li>\n";
                echo "</ol>\n";
                echo "</div>\n";
                
            } else {
                echo "<p style='color: red;'>❌ <strong>Erro na verificação do backup!</strong></p>\n";
                echo "<p>Contagem não confere. Backup pode estar incompleto.</p>\n";
            }
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ <strong>Erro ao criar backup:</strong> " . $e->getMessage() . "</p>\n";
        }
        
        echo "</div>\n";
        
    } else {
        // Mostrar informações antes do backup
        echo "<h3>📊 Informações da Tabela Atual</h3>\n";
        
        $stmt = $pdo->query("
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN status = 'open' THEN 1 END) as open_records,
                COUNT(CASE WHEN status = 'close' THEN 1 END) as closed_records,
                MIN(date) as oldest_date,
                MAX(date) as newest_date,
                ROUND(
                    (DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024, 2
                ) as size_mb
            FROM tec_registers, INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'tec_registers'
        ");
        $table_info = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f0f0f0;'><th>Informação</th><th>Valor</th></tr>\n";
        echo "<tr><td>Total de Registros</td><td>" . number_format($table_info['total_records']) . "</td></tr>\n";
        echo "<tr><td>Registros Abertos</td><td>" . number_format($table_info['open_records']) . "</td></tr>\n";
        echo "<tr><td>Registros Fechados</td><td>" . number_format($table_info['closed_records']) . "</td></tr>\n";
        echo "<tr><td>Data Mais Antiga</td><td>" . date('d/m/Y H:i', strtotime($table_info['oldest_date'])) . "</td></tr>\n";
        echo "<tr><td>Data Mais Recente</td><td>" . date('d/m/Y H:i', strtotime($table_info['newest_date'])) . "</td></tr>\n";
        echo "<tr><td>Tamanho da Tabela</td><td>" . $table_info['size_mb'] . " MB</td></tr>\n";
        echo "</table>\n";
        
        // Verificar se já existem backups
        echo "<h3>📋 Backups Existentes</h3>\n";
        
        $stmt = $pdo->query("SHOW TABLES LIKE 'tec_registers_backup_%'");
        $existing_backups = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if ($existing_backups) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
            echo "<tr style='background: #f0f0f0;'><th>Tabela de Backup</th><th>Registros</th><th>Ações</th></tr>\n";
            
            foreach ($existing_backups as $backup) {
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM `$backup`");
                $stmt->execute();
                $backup_count = $stmt->fetchColumn();
                
                echo "<tr>\n";
                echo "<td><code>$backup</code></td>\n";
                echo "<td>" . number_format($backup_count) . "</td>\n";
                echo "<td>\n";
                echo "<a href='restore_backup_$backup.php' target='_blank' style='color: #2196F3; margin-right: 10px;'>🔄 Restaurar</a>\n";
                echo "<a href='#' onclick=\"if(confirm('Tem certeza que deseja remover este backup?')) { window.location.href='?remove_backup=$backup'; }\" style='color: #f44336;'>🗑️ Remover</a>\n";
                echo "</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p>Nenhum backup existente encontrado.</p>\n";
        }
        
        // Formulário para criar backup
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 20px 0;'>\n";
        echo "<h3>💾 Criar Novo Backup</h3>\n";
        echo "<p><strong>Nome da tabela de backup:</strong> <code>$backup_table</code></p>\n";
        echo "<p>Este backup incluirá todos os " . number_format($table_info['total_records']) . " registros da tabela original.</p>\n";
        
        echo "<form method='post'>\n";
        echo "<input type='hidden' name='create_backup' value='1'>\n";
        echo "<button type='submit' style='background: #4caf50; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>💾 Criar Backup Agora</button>\n";
        echo "</form>\n";
        echo "</div>\n";
        
        echo "<div style='background: #e3f2fd; padding: 15px; border: 1px solid #2196F3; margin: 10px 0;'>\n";
        echo "<h4>ℹ️ Sobre o Backup:</h4>\n";
        echo "<ul>\n";
        echo "<li>O backup será criado na mesma base de dados</li>\n";
        echo "<li>Todos os dados e estrutura serão copiados</li>\n";
        echo "<li>Um script de restauração será gerado automaticamente</li>\n";
        echo "<li>O processo é seguro e não afeta a tabela original</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
    }
    
    // Processar remoção de backup
    if (isset($_GET['remove_backup'])) {
        $backup_to_remove = $_GET['remove_backup'];
        
        // Validar nome da tabela (segurança)
        if (preg_match('/^tec_registers_backup_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}$/', $backup_to_remove)) {
            try {
                $stmt = $pdo->prepare("DROP TABLE `$backup_to_remove`");
                $stmt->execute();
                
                // Remover script de restauração
                $restore_file = "restore_backup_$backup_to_remove.php";
                if (file_exists($restore_file)) {
                    unlink($restore_file);
                }
                
                echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; margin: 10px 0;'>\n";
                echo "<p>✅ Backup <code>$backup_to_remove</code> removido com sucesso!</p>\n";
                echo "<script>setTimeout(function(){ window.location.href = window.location.pathname; }, 2000);</script>\n";
                echo "</div>\n";
                
            } catch (PDOException $e) {
                echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000; margin: 10px 0;'>\n";
                echo "<p>❌ Erro ao remover backup: " . $e->getMessage() . "</p>\n";
                echo "</div>\n";
            }
        } else {
            echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000; margin: 10px 0;'>\n";
            echo "<p>❌ Nome de backup inválido!</p>\n";
            echo "</div>\n";
        }
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000; margin: 20px 0;'>\n";
    echo "<h3>❌ Erro de Conexão</h3>\n";
    echo "<p><strong>Erro:</strong> " . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<p><strong>Scripts de correção:</strong></p>\n";
echo "<ul>\n";
echo "<li><a href='analyze_old_data.php' target='_blank'>📊 Analisar Dados Antigos</a></li>\n";
echo "<li><a href='fix_old_reports.php' target='_blank'>🔧 Corrigir Relatórios Antigos</a></li>\n";
echo "<li><a href='fix_register_totals.php' target='_blank'>🔧 Corrigir Totais Recentes</a></li>\n";
echo "<li><a href='reports/registers' target='_blank'>📊 Página de Relatórios</a></li>\n";
echo "</ul>\n";
?>
