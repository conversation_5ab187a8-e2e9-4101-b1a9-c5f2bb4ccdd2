<?php
// Menu principal para correção de relatórios
// Centraliza todos os scripts de correção e diagnóstico

require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=$PDV_HOST;dbname=$PDV_BASE;charset=utf8", $PDV_USUARIO, $PDV_SENHA);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $connection_ok = true;
} catch (PDOException $e) {
    $connection_ok = false;
    $connection_error = $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menu de Correção de Relatórios</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .status-card {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 5px solid;
        }
        .status-ok { border-color: #4caf50; background: #e8f5e8; }
        .status-warning { border-color: #ff9800; background: #fff3cd; }
        .status-error { border-color: #f44336; background: #ffeeee; }
        .status-info { border-color: #2196F3; background: #e3f2fd; }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .card h3 {
            margin-top: 0;
            color: #333;
        }
        .card p {
            color: #666;
            line-height: 1.5;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            cursor: pointer;
            border: none;
        }
        .btn-primary { background: #2196F3; color: white; }
        .btn-success { background: #4caf50; color: white; }
        .btn-warning { background: #ff9800; color: white; }
        .btn-danger { background: #f44336; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        
        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        .icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2196F3;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Menu de Correção de Relatórios</h1>
            <p>Central de diagnóstico e correção para problemas de formatação e cálculo nos relatórios</p>
        </div>

        <?php if (!$connection_ok): ?>
            <div class="status-card status-error">
                <h3>❌ Erro de Conexão com o Banco de Dados</h3>
                <p><strong>Erro:</strong> <?= htmlspecialchars($connection_error) ?></p>
                <p>Verifique se o MySQL está rodando na porta correta (33306) e se as credenciais estão corretas.</p>
            </div>
        <?php else: ?>
            
            <?php
            // Verificar status geral do sistema
            $stmt = $pdo->query("
                SELECT 
                    COUNT(*) as total_registers,
                    COUNT(CASE WHEN status = 'close' THEN 1 END) as closed_registers,
                    COUNT(CASE WHEN status = 'close' AND (total_cash IS NULL OR total_stripe IS NULL OR total_CC IS NULL OR total_pix IS NULL) THEN 1 END) as need_correction
                FROM tec_registers
            ");
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $stmt = $pdo->query("SELECT decimals, thousands_sep, decimals_sep FROM tec_settings WHERE setting_id = 1");
            $format_settings = $stmt->fetch(PDO::FETCH_ASSOC);
            $format_ok = ($format_settings && $format_settings['thousands_sep'] == '.' && $format_settings['decimals_sep'] == ',');
            ?>
            
            <div class="status-card <?= $format_ok ? 'status-ok' : 'status-warning' ?>">
                <h3><?= $format_ok ? '✅' : '⚠️' ?> Status da Formatação Monetária</h3>
                <p>
                    <?php if ($format_ok): ?>
                        Formatação configurada corretamente para o padrão brasileiro (1.234,56)
                    <?php else: ?>
                        Formatação precisa ser corrigida para o padrão brasileiro
                    <?php endif; ?>
                </p>
            </div>
            
            <div class="status-card <?= $stats['need_correction'] == 0 ? 'status-ok' : 'status-warning' ?>">
                <h3><?= $stats['need_correction'] == 0 ? '✅' : '⚠️' ?> Status dos Totais dos Registros</h3>
                <p>
                    <?php if ($stats['need_correction'] == 0): ?>
                        Todos os registros estão com totais calculados corretamente
                    <?php else: ?>
                        <?= $stats['need_correction'] ?> registros precisam ter os totais recalculados
                    <?php endif; ?>
                </p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['total_registers']) ?></div>
                    <div class="stat-label">Total de Registros</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['closed_registers']) ?></div>
                    <div class="stat-label">Registros Fechados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: <?= $stats['need_correction'] > 0 ? '#f44336' : '#4caf50' ?>">
                        <?= number_format($stats['need_correction']) ?>
                    </div>
                    <div class="stat-label">Precisam Correção</div>
                </div>
            </div>
        <?php endif; ?>

        <div class="grid">
            <!-- Diagnóstico -->
            <div class="card">
                <h3><span class="icon">🔍</span>Diagnóstico</h3>
                <p>Scripts para identificar problemas e analisar dados históricos.</p>
                <div>
                    <a href="analyze_old_data.php" target="_blank" class="btn btn-info">📊 Análise Completa</a>
                    <a href="debug_register_problem.php" target="_blank" class="btn btn-info">🔍 Debug Específico</a>
                </div>
            </div>

            <!-- Backup -->
            <div class="card">
                <h3><span class="icon">💾</span>Backup e Segurança</h3>
                <p>Criar backup de segurança antes de executar correções.</p>
                <div>
                    <a href="backup_before_fix.php" target="_blank" class="btn btn-warning">💾 Criar Backup</a>
                </div>
                <p><small><em>⚠️ Recomendado antes de qualquer correção</em></small></p>
            </div>

            <!-- Formatação -->
            <div class="card">
                <h3><span class="icon">🎨</span>Correção de Formatação</h3>
                <p>Corrigir formatação monetária para o padrão brasileiro.</p>
                <div>
                    <a href="test_format_simple.php" target="_blank" class="btn btn-primary">🧪 Teste Simples</a>
                    <a href="fix_currency_format.php" target="_blank" class="btn btn-success">🔧 Correção Completa</a>
                </div>
            </div>

            <!-- Totais Recentes -->
            <div class="card">
                <h3><span class="icon">🔧</span>Correção de Totais (Recentes)</h3>
                <p>Corrigir totais incorretos em registros recentes.</p>
                <div>
                    <a href="fix_register_totals.php" target="_blank" class="btn btn-success">🔧 Corrigir Recentes</a>
                </div>
                <p><small><em>Para registros dos últimos meses</em></small></p>
            </div>

            <!-- Totais Antigos -->
            <div class="card">
                <h3><span class="icon">⚙️</span>Correção de Totais (Antigos)</h3>
                <p>Correção especializada para registros históricos com processamento em lotes.</p>
                <div>
                    <a href="fix_old_reports.php" target="_blank" class="btn btn-warning">⚙️ Corrigir Antigos</a>
                </div>
                <p><small><em>Processamento em lotes para evitar timeout</em></small></p>
            </div>

            <!-- Verificação Final -->
            <div class="card">
                <h3><span class="icon">✅</span>Verificação Final</h3>
                <p>Verificar se todas as correções foram aplicadas corretamente.</p>
                <div>
                    <a href="reports/registers" target="_blank" class="btn btn-primary">📊 Ver Relatórios</a>
                    <a href="test_reports_format.php" target="_blank" class="btn btn-info">🧪 Teste Formatação</a>
                </div>
            </div>
        </div>

        <div class="status-card status-info">
            <h3>📋 Ordem Recomendada de Execução</h3>
            <ol>
                <li><strong>Backup:</strong> Criar backup de segurança</li>
                <li><strong>Diagnóstico:</strong> Analisar dados para identificar problemas</li>
                <li><strong>Formatação:</strong> Corrigir configurações de formatação monetária</li>
                <li><strong>Totais:</strong> Corrigir totais incorretos (recentes primeiro, depois antigos)</li>
                <li><strong>Verificação:</strong> Confirmar que tudo está funcionando corretamente</li>
            </ol>
        </div>

        <div class="status-card status-info">
            <h3>ℹ️ Informações Importantes</h3>
            <ul>
                <li><strong>Backup:</strong> Sempre faça backup antes de executar correções</li>
                <li><strong>Processamento:</strong> Scripts de correção antigos processam em lotes para evitar timeout</li>
                <li><strong>Segurança:</strong> Todos os scripts são seguros e não afetam dados originais sem confirmação</li>
                <li><strong>Restauração:</strong> Scripts de backup geram automaticamente scripts de restauração</li>
            </ul>
        </div>

        <div class="status-card status-info">
            <h3>📚 Documentação</h3>
            <p>Para informações detalhadas sobre os problemas e soluções, consulte:</p>
            <a href="SOLUCAO_FORMATACAO.md" target="_blank" class="btn btn-info">📖 Documentação Completa</a>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #666;">
            <p><small>Sistema de Correção de Relatórios - Versão 1.0</small></p>
        </div>
    </div>
</body>
</html>
