<?php
// Script para corrigir formatação monetária
// Problema: valores como 31.378,00 aparecem como 313,78

echo "<h2>Diagnóstico e Correção da Formatação Monetária</h2>\n";

// Verificar se o MySQL está rodando
require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=$PDV_HOST;dbname=$PDV_BASE;charset=utf8", $PDV_USUARIO, $PDV_SENHA);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $mysql_running = true;

    echo "<h2>Corrigindo formatação monetária - Relatório de Registros</h2>\n";

    // Verificar configurações atuais
    $stmt = $pdo->prepare("SELECT decimals, thousands_sep, decimals_sep FROM tec_settings WHERE setting_id = 1");
    $stmt->execute();
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($settings) {
        echo "<h3>Configurações atuais no banco:</h3>\n";
        echo "- Decimais: " . $settings['decimals'] . "<br>\n";
        echo "- Separador de milhares: '" . $settings['thousands_sep'] . "'<br>\n";
        echo "- Separador decimal: '" . $settings['decimals_sep'] . "'<br>\n";

        // Forçar correção para o padrão brasileiro
        echo "<br><h3>Aplicando correção para padrão brasileiro...</h3>\n";

        $stmt = $pdo->prepare("UPDATE tec_settings SET thousands_sep = '.', decimals_sep = ',' WHERE setting_id = 1");
        $result = $stmt->execute();

        if ($result) {
            echo "<span style='color: green; font-weight: bold;'>✓ Configurações atualizadas com sucesso!</span><br>\n";
            echo "- Separador de milhares: '.' (ponto)<br>\n";
            echo "- Separador decimal: ',' (vírgula)<br>\n";

            // Verificar se foi realmente atualizado
            $stmt = $pdo->prepare("SELECT decimals, thousands_sep, decimals_sep FROM tec_settings WHERE setting_id = 1");
            $stmt->execute();
            $new_settings = $stmt->fetch(PDO::FETCH_ASSOC);

            echo "<br><h3>Configurações após atualização:</h3>\n";
            echo "- Decimais: " . $new_settings['decimals'] . "<br>\n";
            echo "- Separador de milhares: '" . $new_settings['thousands_sep'] . "'<br>\n";
            echo "- Separador decimal: '" . $new_settings['decimals_sep'] . "'<br>\n";

        } else {
            echo "<span style='color: red;'>Erro ao atualizar configurações!</span><br>\n";
        }

    } else {
        echo "<span style='color: red;'>Erro: Não foi possível encontrar as configurações.</span><br>\n";
    }

    echo "<br><h3>Teste de formatação com padrão brasileiro:</h3>\n";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>Valor Original</th><th>Formatado (Correto)</th><th>Exemplo Problema</th></tr>\n";

    // Testar formatação com diferentes valores
    $test_values = [31378, 1234.56, 999.99, 10000, 313.78];

    foreach ($test_values as $value) {
        $correct_format = number_format($value, 2, ',', '.');
        $wrong_format = number_format($value, 2, '.', ',');
        echo "<tr>\n";
        echo "<td>$value</td>\n";
        echo "<td style='color: green; font-weight: bold;'>$correct_format</td>\n";
        echo "<td style='color: red;'>$wrong_format</td>\n";
        echo "</tr>\n";
    }
    echo "</table>\n";

} catch (PDOException $e) {
    $mysql_running = false;
    echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000; margin: 10px 0;'>\n";
    echo "<h3 style='color: red;'>⚠️ MySQL não está rodando</h3>\n";
    echo "<p><strong>Erro:</strong> " . $e->getMessage() . "</p>\n";
    echo "<p><strong>Solução:</strong></p>\n";
    echo "<ol>\n";
    echo "<li>Abra o Painel de Controle do XAMPP</li>\n";
    echo "<li>Clique em 'Start' ao lado de MySQL</li>\n";
    echo "<li>Aguarde até aparecer 'Running' em verde</li>\n";
    echo "<li>Recarregue esta página</li>\n";
    echo "</ol>\n";
    echo "</div>\n";

    // Mostrar instruções manuais
    echo "<h3>Correção Manual (enquanto o MySQL não estiver rodando):</h3>\n";
    echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #0066cc; margin: 10px 0;'>\n";
    echo "<p>Execute este comando SQL quando o MySQL estiver funcionando:</p>\n";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ccc;'>\n";
    echo "UPDATE tec_settings SET thousands_sep = '.', decimals_sep = ',' WHERE setting_id = 1;\n";
    echo "</pre>\n";
    echo "</div>\n";
}

echo "<br><h3 style='color: blue;'>Próximos passos:</h3>\n";
echo "<ol>\n";
echo "<li><strong>Limpe o cache do navegador</strong> (Ctrl+F5 ou Ctrl+Shift+R)</li>\n";
echo "<li><strong>Feche e abra novamente o navegador</strong></li>\n";
echo "<li><strong>Acesse a página de relatórios:</strong> <a href='reports/registers' target='_blank'>http://localhost:8010/sistema/reports/registers</a></li>\n";
echo "<li><strong>Verifique se os valores agora aparecem corretamente</strong></li>\n";
echo "</ol>\n";

echo "<br><p style='background: #f0f8ff; padding: 10px; border: 1px solid #ccc;'>\n";
echo "<strong>Explicação do problema:</strong><br>\n";
echo "O sistema estava usando separadores no padrão americano (vírgula para milhares, ponto para decimais) em vez do padrão brasileiro (ponto para milhares, vírgula para decimais).<br>\n";
echo "Exemplo: 31378 estava sendo formatado como 31,378.00 em vez de 31.378,00\n";
echo "</p>\n";
?>
