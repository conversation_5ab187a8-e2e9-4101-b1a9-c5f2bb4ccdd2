<?php
// Script para correção de relatórios mais antigos
// Este script trata casos especiais de registros antigos com dados inconsistentes

require_once 'config.php';

// Configurações do script
$BATCH_SIZE = 20; // Processar em lotes para evitar timeout
$MAX_EXECUTION_TIME = 300; // 5 minutos máximo
set_time_limit($MAX_EXECUTION_TIME);

try {
    $pdo = new PDO("mysql:host=$PDV_HOST;dbname=$PDV_BASE;charset=utf8", $PDV_USUARIO, $PDV_SENHA);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔧 Correção de Relatórios Antigos</h2>\n";
    echo "<p><em>Script especializado para corrigir registros antigos com dados inconsistentes</em></p>\n";
    
    // Verificar estrutura da tabela
    echo "<h3>📋 Verificando estrutura da tabela...</h3>\n";
    
    $required_fields = ['total_cash', 'total_stripe', 'total_CC', 'total_pix'];
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'tec_registers' AND COLUMN_NAME = ?");
        $stmt->execute([$field]);
        if ($stmt->fetchColumn() == 0) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000; margin: 10px 0;'>\n";
        echo "<h3>❌ Campos Ausentes na Tabela</h3>\n";
        echo "<p>Os seguintes campos precisam ser adicionados à tabela tec_registers:</p>\n";
        echo "<ul>\n";
        foreach ($missing_fields as $field) {
            echo "<li><code>$field</code></li>\n";
        }
        echo "</ul>\n";
        
        if (isset($_POST['add_fields'])) {
            echo "<p>Adicionando campos...</p>\n";
            
            $alter_statements = [];
            foreach ($missing_fields as $field) {
                $alter_statements[] = "ADD COLUMN `$field` decimal(25,2) DEFAULT NULL";
            }
            
            $sql = "ALTER TABLE `tec_registers` " . implode(', ', $alter_statements);
            $pdo->exec($sql);
            
            echo "<p style='color: green;'>✅ Campos adicionados com sucesso!</p>\n";
            echo "<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>\n";
        } else {
            echo "<form method='post'>\n";
            echo "<input type='hidden' name='add_fields' value='1'>\n";
            echo "<button type='submit' style='background: #ff9800; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Adicionar Campos</button>\n";
            echo "</form>\n";
            echo "</div>\n";
            exit;
        }
    } else {
        echo "<p style='color: green;'>✅ Todos os campos necessários existem.</p>\n";
    }
    
    // Buscar registros antigos que precisam de correção
    echo "<h3>🔍 Analisando registros antigos...</h3>\n";
    
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_registers,
            COUNT(CASE WHEN status = 'close' THEN 1 END) as closed_registers,
            COUNT(CASE WHEN status = 'close' AND (total_cash IS NULL OR total_stripe IS NULL OR total_CC IS NULL OR total_pix IS NULL) THEN 1 END) as need_correction,
            MIN(date) as oldest_date,
            MAX(date) as newest_date
        FROM tec_registers
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #2196F3; margin: 10px 0;'>\n";
    echo "<h4>📊 Estatísticas dos Registros:</h4>\n";
    echo "<ul>\n";
    echo "<li><strong>Total de registros:</strong> " . $stats['total_registers'] . "</li>\n";
    echo "<li><strong>Registros fechados:</strong> " . $stats['closed_registers'] . "</li>\n";
    echo "<li><strong>Precisam correção:</strong> " . $stats['need_correction'] . "</li>\n";
    echo "<li><strong>Período:</strong> " . date('d/m/Y', strtotime($stats['oldest_date'])) . " até " . date('d/m/Y', strtotime($stats['newest_date'])) . "</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    if ($stats['need_correction'] == 0) {
        echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; margin: 10px 0;'>\n";
        echo "<h3>✅ Nenhuma Correção Necessária</h3>\n";
        echo "<p>Todos os registros já estão com os totais corretos.</p>\n";
        echo "</div>\n";
        exit;
    }
    
    // Processar correção
    if (isset($_POST['fix_old_reports'])) {
        $batch = isset($_POST['batch']) ? (int)$_POST['batch'] : 1;
        $offset = ($batch - 1) * $BATCH_SIZE;
        
        echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; margin: 10px 0;'>\n";
        echo "<h3>🔄 Processando Lote $batch...</h3>\n";
        
        // Buscar registros que precisam de correção
        $stmt = $pdo->prepare("
            SELECT 
                id, date, closed_at, user_id, cash_in_hand,
                total_cash, total_stripe, total_CC, total_pix
            FROM tec_registers 
            WHERE status = 'close' 
            AND closed_at IS NOT NULL
            AND (total_cash IS NULL OR total_stripe IS NULL OR total_CC IS NULL OR total_pix IS NULL)
            ORDER BY date ASC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$BATCH_SIZE, $offset]);
        $registers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($registers)) {
            echo "<p style='color: green;'>✅ Todos os registros foram processados!</p>\n";
            echo "<p><a href='reports/registers' target='_blank' style='background: #4caf50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Ver Relatórios Corrigidos</a></p>\n";
        } else {
            $corrected_count = 0;
            $error_count = 0;
            
            foreach ($registers as $register) {
                $register_id = $register['id'];
                $user_id = $register['user_id'];
                $open_date = $register['date'];
                $close_date = $register['closed_at'];
                
                echo "<div style='border-left: 3px solid #2196F3; padding-left: 10px; margin: 5px 0;'>\n";
                echo "<strong>📊 Registro #$register_id</strong> (Usuário: $user_id)<br>\n";
                echo "Período: " . date('d/m/Y H:i', strtotime($open_date)) . " até " . date('d/m/Y H:i', strtotime($close_date)) . "<br>\n";
                
                try {
                    // Calcular totais reais baseados nos pagamentos
                    $totals = [];
                    
                    // Total Cash (Dinheiro)
                    $stmt_cash = $pdo->prepare("
                        SELECT COALESCE(SUM(amount), 0) as total
                        FROM tec_payments 
                        WHERE date > ? AND date <= ? 
                        AND created_by = ? 
                        AND paid_by = 'cash'
                        AND type != 'returned'
                    ");
                    $stmt_cash->execute([$open_date, $close_date, $user_id]);
                    $totals['cash'] = $stmt_cash->fetchColumn();
                    
                    // Total Stripe (Débito)
                    $stmt_stripe = $pdo->prepare("
                        SELECT COALESCE(SUM(amount), 0) as total
                        FROM tec_payments 
                        WHERE date > ? AND date <= ? 
                        AND created_by = ? 
                        AND paid_by = 'stripe'
                        AND type != 'returned'
                    ");
                    $stmt_stripe->execute([$open_date, $close_date, $user_id]);
                    $totals['stripe'] = $stmt_stripe->fetchColumn();
                    
                    // Total CC (Crédito)
                    $stmt_cc = $pdo->prepare("
                        SELECT COALESCE(SUM(amount), 0) as total
                        FROM tec_payments 
                        WHERE date > ? AND date <= ? 
                        AND created_by = ? 
                        AND paid_by = 'CC'
                        AND type != 'returned'
                    ");
                    $stmt_cc->execute([$open_date, $close_date, $user_id]);
                    $totals['cc'] = $stmt_cc->fetchColumn();
                    
                    // Total PIX
                    $stmt_pix = $pdo->prepare("
                        SELECT COALESCE(SUM(amount), 0) as total
                        FROM tec_payments 
                        WHERE date > ? AND date <= ? 
                        AND created_by = ? 
                        AND paid_by = 'pix'
                        AND type != 'returned'
                    ");
                    $stmt_pix->execute([$open_date, $close_date, $user_id]);
                    $totals['pix'] = $stmt_pix->fetchColumn();
                    
                    // Verificar se há vendas no período (para registros muito antigos)
                    $stmt_sales = $pdo->prepare("
                        SELECT COUNT(*) as sales_count
                        FROM tec_sales 
                        WHERE date > ? AND date <= ? 
                        AND created_by = ?
                    ");
                    $stmt_sales->execute([$open_date, $close_date, $user_id]);
                    $sales_count = $stmt_sales->fetchColumn();
                    
                    // Atualizar registro
                    $stmt_update = $pdo->prepare("
                        UPDATE tec_registers 
                        SET total_cash = ?, total_stripe = ?, total_CC = ?, total_pix = ?
                        WHERE id = ?
                    ");
                    $result = $stmt_update->execute([
                        $totals['cash'], 
                        $totals['stripe'], 
                        $totals['cc'], 
                        $totals['pix'], 
                        $register_id
                    ]);
                    
                    if ($result) {
                        echo "<span style='color: green;'>✅ Corrigido</span><br>\n";
                        echo "&nbsp;&nbsp;Dinheiro: R$ " . number_format($totals['cash'], 2, ',', '.') . "<br>\n";
                        echo "&nbsp;&nbsp;Débito: R$ " . number_format($totals['stripe'], 2, ',', '.') . "<br>\n";
                        echo "&nbsp;&nbsp;Crédito: R$ " . number_format($totals['cc'], 2, ',', '.') . "<br>\n";
                        echo "&nbsp;&nbsp;PIX: R$ " . number_format($totals['pix'], 2, ',', '.') . "<br>\n";
                        echo "&nbsp;&nbsp;Vendas no período: $sales_count<br>\n";
                        $corrected_count++;
                    } else {
                        echo "<span style='color: red;'>❌ Erro ao atualizar</span><br>\n";
                        $error_count++;
                    }
                    
                } catch (Exception $e) {
                    echo "<span style='color: red;'>❌ Erro: " . $e->getMessage() . "</span><br>\n";
                    $error_count++;
                }
                
                echo "</div>\n";
                
                // Flush para mostrar progresso
                if (ob_get_level()) ob_flush();
                flush();
            }
            
            echo "<div style='background: #f0f8ff; padding: 10px; border: 1px solid #2196F3; margin: 10px 0;'>\n";
            echo "<h4>📊 Resultado do Lote $batch:</h4>\n";
            echo "<ul>\n";
            echo "<li>✅ Corrigidos: $corrected_count</li>\n";
            echo "<li>❌ Erros: $error_count</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
            
            // Verificar se há mais registros para processar
            $stmt_remaining = $pdo->prepare("
                SELECT COUNT(*) 
                FROM tec_registers 
                WHERE status = 'close' 
                AND closed_at IS NOT NULL
                AND (total_cash IS NULL OR total_stripe IS NULL OR total_CC IS NULL OR total_pix IS NULL)
                AND id > (SELECT MAX(id) FROM (SELECT id FROM tec_registers WHERE status = 'close' ORDER BY date ASC LIMIT ? OFFSET ?) t)
            ");
            $stmt_remaining->execute([$BATCH_SIZE, $offset]);
            $remaining = $stmt_remaining->fetchColumn();
            
            if ($remaining > 0) {
                echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>\n";
                echo "<h4>⏳ Processamento Contínuo</h4>\n";
                echo "<p>Ainda restam <strong>$remaining</strong> registros para processar.</p>\n";
                echo "<form method='post'>\n";
                echo "<input type='hidden' name='fix_old_reports' value='1'>\n";
                echo "<input type='hidden' name='batch' value='" . ($batch + 1) . "'>\n";
                echo "<button type='submit' style='background: #ff9800; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Processar Próximo Lote (" . ($batch + 1) . ")</button>\n";
                echo "</form>\n";
                echo "</div>\n";
            } else {
                echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; margin: 10px 0;'>\n";
                echo "<h3>🎉 Correção Concluída!</h3>\n";
                echo "<p>Todos os registros antigos foram processados com sucesso.</p>\n";
                echo "<p><a href='reports/registers' target='_blank' style='background: #4caf50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Ver Relatórios Corrigidos</a></p>\n";
                echo "</div>\n";
            }
        }
        
        echo "</div>\n";
        
    } else {
        // Mostrar opções de correção
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>\n";
        echo "<h3>⚠️ Registros Antigos Precisam de Correção</h3>\n";
        echo "<p>Foram encontrados <strong>" . $stats['need_correction'] . "</strong> registros que precisam ter os totais recalculados.</p>\n";
        
        echo "<h4>🔧 Opções de Correção:</h4>\n";
        echo "<div style='margin: 10px 0;'>\n";
        echo "<form method='post' style='display: inline-block; margin-right: 10px;'>\n";
        echo "<input type='hidden' name='fix_old_reports' value='1'>\n";
        echo "<button type='submit' style='background: #ff9800; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>🚀 Iniciar Correção Automática</button>\n";
        echo "</form>\n";
        echo "</div>\n";
        
        echo "<p><small><em>O processamento será feito em lotes de $BATCH_SIZE registros para evitar timeout.</em></small></p>\n";
        echo "</div>\n";
        
        // Mostrar preview dos registros mais antigos
        echo "<h3>👀 Preview dos Registros Mais Antigos:</h3>\n";
        $stmt = $pdo->prepare("
            SELECT 
                id, date, closed_at, user_id, cash_in_hand,
                total_cash, total_stripe, total_CC, total_pix
            FROM tec_registers 
            WHERE status = 'close' 
            AND closed_at IS NOT NULL
            AND (total_cash IS NULL OR total_stripe IS NULL OR total_CC IS NULL OR total_pix IS NULL)
            ORDER BY date ASC
            LIMIT 10
        ");
        $stmt->execute();
        $preview_registers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($preview_registers) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>\n";
            echo "<tr style='background: #f0f0f0;'>\n";
            echo "<th>ID</th><th>Data Abertura</th><th>Data Fechamento</th><th>Usuário</th><th>Caixa Inicial</th><th>Status Totais</th>\n";
            echo "</tr>\n";
            
            foreach ($preview_registers as $reg) {
                $missing_totals = [];
                if (is_null($reg['total_cash'])) $missing_totals[] = 'Dinheiro';
                if (is_null($reg['total_stripe'])) $missing_totals[] = 'Débito';
                if (is_null($reg['total_CC'])) $missing_totals[] = 'Crédito';
                if (is_null($reg['total_pix'])) $missing_totals[] = 'PIX';
                
                echo "<tr>\n";
                echo "<td>" . $reg['id'] . "</td>\n";
                echo "<td>" . date('d/m/Y H:i', strtotime($reg['date'])) . "</td>\n";
                echo "<td>" . date('d/m/Y H:i', strtotime($reg['closed_at'])) . "</td>\n";
                echo "<td>" . $reg['user_id'] . "</td>\n";
                echo "<td style='text-align: right;'>" . number_format($reg['cash_in_hand'], 2, ',', '.') . "</td>\n";
                echo "<td style='color: red;'>❌ Faltam: " . implode(', ', $missing_totals) . "</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000; margin: 20px 0;'>\n";
    echo "<h3>❌ Erro de Conexão</h3>\n";
    echo "<p><strong>Erro:</strong> " . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<p><strong>Links úteis:</strong></p>\n";
echo "<ul>\n";
echo "<li><a href='reports/registers' target='_blank'>Página de Relatórios de Registros</a></li>\n";
echo "<li><a href='fix_register_totals.php' target='_blank'>Correção de Totais (Registros Recentes)</a></li>\n";
echo "<li><a href='debug_register_problem.php' target='_blank'>Debug do Problema</a></li>\n";
echo "</ul>\n";
?>
