<?php
// Teste para verificar se a correção da formatação na página detalhado funcionou
require_once 'app/config/database.php';

// Conectar ao banco
$host = $db['default']['hostname'];
$user = $db['default']['username'];
$pass = $db['default']['password'];
$database = $db['default']['database'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Teste da Correção - Página Detalhado</h2>";
    
    // Buscar algumas vendas recentes
    $stmt = $pdo->prepare("
        SELECT 
            s.id, 
            s.date, 
            s.grand_total, 
            s.customer_name,
            u.first_name,
            u.last_name
        FROM sales s
        LEFT JOIN users u ON s.created_by = u.id
        ORDER BY s.id DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $vendas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($vendas) {
        echo "<h3>Valores como aparecem na página detalhado:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
        echo "<tr style='background: #f0f0f0;'>\n";
        echo "<th>ID</th><th>Data</th><th>Cliente</th><th>Usuário</th><th>Total (Raw)</th><th>Total (Formatado PHP)</th>\n";
        echo "</tr>\n";
        
        // Incluir a biblioteca Tec para formatação
        require_once 'app/libraries/Tec.php';
        $tec = new Tec();
        
        foreach ($vendas as $venda) {
            echo "<tr>\n";
            echo "<td>" . htmlspecialchars($venda['id']) . "</td>\n";
            echo "<td>" . htmlspecialchars($venda['date']) . "</td>\n";
            echo "<td>" . htmlspecialchars($venda['customer_name']) . "</td>\n";
            echo "<td>" . htmlspecialchars($venda['first_name'] . ' ' . $venda['last_name']) . "</td>\n";
            echo "<td style='text-align: right;'>" . htmlspecialchars($venda['grand_total']) . "</td>\n";
            echo "<td style='text-align: right; font-weight: bold; color: green;'>" . htmlspecialchars($tec->formatMoney($venda['grand_total'])) . "</td>\n";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
        
        echo "<h3>Explicação da Correção:</h3>";
        echo "<p><strong>Problema:</strong> A página detalhado estava aplicando formatação JavaScript (currencyFormat) em dados que já estavam formatados pelo PHP.</p>";
        echo "<p><strong>Solução:</strong> Removemos a formatação JavaScript da coluna Total no DataTable, deixando apenas a formatação PHP que já funciona corretamente.</p>";
        echo "<p><strong>Resultado:</strong> Os valores agora devem aparecer corretamente com centavos, como mostrado na coluna 'Total (Formatado PHP)' acima.</p>";
        
        echo "<h3>Verificação:</h3>";
        echo "<p>1. Os valores na coluna 'Total (Raw)' são como estão armazenados no banco de dados</p>";
        echo "<p>2. Os valores na coluna 'Total (Formatado PHP)' são como devem aparecer na página detalhado</p>";
        echo "<p>3. Se você ver valores como '31.378,00' na coluna formatada, a correção funcionou!</p>";
        
    } else {
        echo "<p>Nenhuma venda encontrada para teste.</p>";
    }
    
} catch (PDOException $e) {
    echo "Erro de conexão: " . $e->getMessage();
}
?>
