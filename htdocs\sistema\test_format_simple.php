<?php
// Teste simples de formatação monetária

require_once 'config.php';

echo "<h2>Teste Simples de Formatação Monetária</h2>\n";

try {
    $pdo = new PDO("mysql:host=$PDV_HOST;dbname=$PDV_BASE;charset=utf8", $PDV_USUARIO, $PDV_SENHA);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Buscar configurações
    $stmt = $pdo->prepare("SELECT decimals, thousands_sep, decimals_sep FROM tec_settings WHERE setting_id = 1");
    $stmt->execute();
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($settings) {
        echo "<h3>✅ Conexão com banco OK!</h3>\n";
        echo "<p><strong>Configurações atuais:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>Decimais: " . $settings['decimals'] . "</li>\n";
        echo "<li>Separador de milhares: '" . $settings['thousands_sep'] . "'</li>\n";
        echo "<li>Separador decimal: '" . $settings['decimals_sep'] . "'</li>\n";
        echo "</ul>\n";
        
        // Teste de valores
        $test_values = [31378, 1234.56, 999.99, 10000, 313.78];
        
        echo "<h3>Teste de Formatação:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f0f0f0;'>\n";
        echo "<th>Valor Original</th>\n";
        echo "<th>Formatação PHP (Atual)</th>\n";
        echo "<th>Formatação Correta (Brasil)</th>\n";
        echo "<th>Status</th>\n";
        echo "</tr>\n";
        
        foreach ($test_values as $value) {
            $current_format = number_format($value, $settings['decimals'], $settings['decimals_sep'], $settings['thousands_sep']);
            $correct_format = number_format($value, 2, ',', '.');
            $is_correct = ($current_format === $correct_format);
            
            echo "<tr>\n";
            echo "<td>$value</td>\n";
            echo "<td style='font-weight: bold; color: " . ($is_correct ? 'green' : 'red') . ";'>$current_format</td>\n";
            echo "<td style='color: green;'>$correct_format</td>\n";
            echo "<td>" . ($is_correct ? "✅ Correto" : "❌ Incorreto") . "</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        // Verificar se precisa corrigir
        $needs_fix = ($settings['thousands_sep'] !== '.' || $settings['decimals_sep'] !== ',');
        
        if ($needs_fix) {
            echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000; margin: 20px 0;'>\n";
            echo "<h3>⚠️ Configuração Incorreta Detectada!</h3>\n";
            echo "<p>As configurações estão no padrão americano em vez do brasileiro.</p>\n";
            echo "<p><strong>Clique no botão abaixo para corrigir:</strong></p>\n";
            echo "<form method='post' style='margin: 10px 0;'>\n";
            echo "<input type='hidden' name='fix_format' value='1'>\n";
            echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Corrigir Formatação</button>\n";
            echo "</form>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #eeffee; padding: 15px; border: 1px solid #00aa00; margin: 20px 0;'>\n";
            echo "<h3>✅ Configuração Correta!</h3>\n";
            echo "<p>As configurações estão no padrão brasileiro correto.</p>\n";
            echo "</div>\n";
        }
        
        // Processar correção se solicitada
        if (isset($_POST['fix_format'])) {
            echo "<h3>Aplicando Correção...</h3>\n";
            $stmt = $pdo->prepare("UPDATE tec_settings SET thousands_sep = '.', decimals_sep = ',' WHERE setting_id = 1");
            $result = $stmt->execute();
            
            if ($result) {
                echo "<div style='background: #eeffee; padding: 15px; border: 1px solid #00aa00; margin: 20px 0;'>\n";
                echo "<h3>✅ Correção Aplicada com Sucesso!</h3>\n";
                echo "<p>As configurações foram atualizadas para o padrão brasileiro.</p>\n";
                echo "<p><strong>Próximos passos:</strong></p>\n";
                echo "<ol>\n";
                echo "<li>Limpe o cache do navegador (Ctrl+F5)</li>\n";
                echo "<li>Recarregue esta página para verificar</li>\n";
                echo "<li>Teste a página de relatórios</li>\n";
                echo "</ol>\n";
                echo "</div>\n";
                echo "<script>setTimeout(function(){ window.location.reload(); }, 3000);</script>\n";
            } else {
                echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000; margin: 20px 0;'>\n";
                echo "<h3>❌ Erro ao Aplicar Correção</h3>\n";
                echo "<p>Não foi possível atualizar as configurações.</p>\n";
                echo "</div>\n";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ Não foi possível encontrar as configurações no banco.</p>\n";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000; margin: 20px 0;'>\n";
    echo "<h3>❌ Erro de Conexão com o Banco</h3>\n";
    echo "<p><strong>Erro:</strong> " . $e->getMessage() . "</p>\n";
    echo "<p><strong>Verifique:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Se o MySQL está rodando na porta 3306</li>\n";
    echo "<li>Se o banco 'sis_nfe_pdv' existe</li>\n";
    echo "<li>Se as credenciais estão corretas</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<p><strong>Links úteis:</strong></p>\n";
echo "<ul>\n";
echo "<li><a href='reports/registers' target='_blank'>Página de Relatórios de Registros</a></li>\n";
echo "<li><a href='test_format_simple.php'>Recarregar este teste</a></li>\n";
echo "</ul>\n";
?>
