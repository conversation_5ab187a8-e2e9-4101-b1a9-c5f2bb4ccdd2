<?php
// Script para corrigir os totais incorretos na tabela tec_registers

require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=$PDV_HOST;dbname=$PDV_BASE;charset=utf8", $PDV_USUARIO, $PDV_SENHA);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔧 Correção dos Totais na Tabela tec_registers</h2>\n";
    
    // Verificar se há registros para corrigir
    $stmt = $pdo->prepare("
        SELECT 
            id, date, closed_at, user_id, 
            cash_in_hand, total_cash, total_stripe, total_CC, total_pix
        FROM tec_registers 
        WHERE status = 'close' 
        AND closed_at IS NOT NULL
        ORDER BY id DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $registers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!$registers) {
        echo "<p style='color: orange;'>⚠️ Nenhum registro de caixa fechado encontrado.</p>\n";
        exit;
    }
    
    echo "<h3>📋 Registros que serão verificados/corrigidos:</h3>\n";
    echo "<p>Encontrados <strong>" . count($registers) . "</strong> registros fechados.</p>\n";
    
    if (isset($_POST['fix_totals'])) {
        echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; margin: 10px 0;'>\n";
        echo "<h3>🔄 Processando Correções...</h3>\n";
        
        $corrected_count = 0;
        $skipped_count = 0;
        
        foreach ($registers as $register) {
            $register_id = $register['id'];
            $user_id = $register['user_id'];
            $open_date = $register['date'];
            $close_date = $register['closed_at'];
            
            echo "<div style='border-left: 3px solid #2196F3; padding-left: 10px; margin: 10px 0;'>\n";
            echo "<strong>📊 Processando Registro #$register_id</strong><br>\n";
            echo "Usuário: $user_id | Período: " . date('d/m/Y H:i', strtotime($open_date)) . " até " . date('d/m/Y H:i', strtotime($close_date)) . "<br>\n";
            
            // Calcular totais reais baseados nos pagamentos
            $stmt_cash = $pdo->prepare("
                SELECT SUM(COALESCE(amount, 0)) as total
                FROM tec_payments 
                WHERE date > ? AND date <= ? AND created_by = ? AND paid_by = 'cash'
            ");
            $stmt_cash->execute([$open_date, $close_date, $user_id]);
            $real_cash = $stmt_cash->fetchColumn() ?: 0;
            
            $stmt_stripe = $pdo->prepare("
                SELECT SUM(COALESCE(amount, 0)) as total
                FROM tec_payments 
                WHERE date > ? AND date <= ? AND created_by = ? AND paid_by = 'stripe'
            ");
            $stmt_stripe->execute([$open_date, $close_date, $user_id]);
            $real_stripe = $stmt_stripe->fetchColumn() ?: 0;
            
            $stmt_cc = $pdo->prepare("
                SELECT SUM(COALESCE(amount, 0)) as total
                FROM tec_payments 
                WHERE date > ? AND date <= ? AND created_by = ? AND paid_by = 'CC'
            ");
            $stmt_cc->execute([$open_date, $close_date, $user_id]);
            $real_cc = $stmt_cc->fetchColumn() ?: 0;
            
            $stmt_pix = $pdo->prepare("
                SELECT SUM(COALESCE(amount, 0)) as total
                FROM tec_payments 
                WHERE date > ? AND date <= ? AND created_by = ? AND paid_by = 'pix'
            ");
            $stmt_pix->execute([$open_date, $close_date, $user_id]);
            $real_pix = $stmt_pix->fetchColumn() ?: 0;
            
            // Comparar com valores armazenados
            $stored_cash = $register['total_cash'] ?: 0;
            $stored_stripe = $register['total_stripe'] ?: 0;
            $stored_cc = $register['total_CC'] ?: 0;
            $stored_pix = $register['total_pix'] ?: 0;
            
            $needs_update = false;
            $changes = [];
            
            if (abs($stored_cash - $real_cash) > 0.01) {
                $needs_update = true;
                $changes[] = "Dinheiro: " . number_format($stored_cash, 2, ',', '.') . " → " . number_format($real_cash, 2, ',', '.');
            }
            
            if (abs($stored_stripe - $real_stripe) > 0.01) {
                $needs_update = true;
                $changes[] = "Débito: " . number_format($stored_stripe, 2, ',', '.') . " → " . number_format($real_stripe, 2, ',', '.');
            }
            
            if (abs($stored_cc - $real_cc) > 0.01) {
                $needs_update = true;
                $changes[] = "Crédito: " . number_format($stored_cc, 2, ',', '.') . " → " . number_format($real_cc, 2, ',', '.');
            }
            
            if (abs($stored_pix - $real_pix) > 0.01) {
                $needs_update = true;
                $changes[] = "PIX: " . number_format($stored_pix, 2, ',', '.') . " → " . number_format($real_pix, 2, ',', '.');
            }
            
            if ($needs_update) {
                // Atualizar registro
                $stmt_update = $pdo->prepare("
                    UPDATE tec_registers 
                    SET total_cash = ?, total_stripe = ?, total_CC = ?, total_pix = ?
                    WHERE id = ?
                ");
                $result = $stmt_update->execute([$real_cash, $real_stripe, $real_cc, $real_pix, $register_id]);
                
                if ($result) {
                    echo "<span style='color: green;'>✅ Corrigido!</span><br>\n";
                    foreach ($changes as $change) {
                        echo "&nbsp;&nbsp;• $change<br>\n";
                    }
                    $corrected_count++;
                } else {
                    echo "<span style='color: red;'>❌ Erro ao atualizar</span><br>\n";
                }
            } else {
                echo "<span style='color: blue;'>ℹ️ Já está correto</span><br>\n";
                $skipped_count++;
            }
            
            echo "</div>\n";
            
            // Flush para mostrar progresso
            if (ob_get_level()) ob_flush();
            flush();
        }
        
        echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #2196F3; margin: 20px 0;'>\n";
        echo "<h3>📊 Resumo da Correção:</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Registros corrigidos:</strong> $corrected_count</li>\n";
        echo "<li><strong>Registros já corretos:</strong> $skipped_count</li>\n";
        echo "<li><strong>Total processado:</strong> " . ($corrected_count + $skipped_count) . "</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        if ($corrected_count > 0) {
            echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; margin: 20px 0;'>\n";
            echo "<h3>🎉 Correção Concluída!</h3>\n";
            echo "<p>Os totais foram corrigidos com sucesso. Agora:</p>\n";
            echo "<ol>\n";
            echo "<li>Limpe o cache do navegador</li>\n";
            echo "<li>Acesse a página de relatórios</li>\n";
            echo "<li>Verifique se os valores estão corretos</li>\n";
            echo "</ol>\n";
            echo "<p><a href='reports/registers' target='_blank' style='background: #4caf50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Ver Relatórios</a></p>\n";
            echo "</div>\n";
        }
        
    } else {
        // Mostrar preview dos dados antes da correção
        echo "<h3>🔍 Preview dos Dados (antes da correção):</h3>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>\n";
        echo "<tr style='background: #f0f0f0;'>\n";
        echo "<th>ID</th><th>Data</th><th>Dinheiro<br>(Armazenado)</th><th>Dinheiro<br>(Real)</th><th>Status</th>\n";
        echo "</tr>\n";
        
        $total_problems = 0;
        
        foreach ($registers as $register) {
            $register_id = $register['id'];
            $user_id = $register['user_id'];
            $open_date = $register['date'];
            $close_date = $register['closed_at'];
            
            // Calcular total real de dinheiro
            $stmt_cash = $pdo->prepare("
                SELECT SUM(COALESCE(amount, 0)) as total
                FROM tec_payments 
                WHERE date > ? AND date <= ? AND created_by = ? AND paid_by = 'cash'
            ");
            $stmt_cash->execute([$open_date, $close_date, $user_id]);
            $real_cash = $stmt_cash->fetchColumn() ?: 0;
            
            $stored_cash = $register['total_cash'] ?: 0;
            $has_problem = abs($stored_cash - $real_cash) > 0.01;
            
            if ($has_problem) $total_problems++;
            
            echo "<tr style='background: " . ($has_problem ? '#ffeeee' : '#eeffee') . ";'>\n";
            echo "<td>$register_id</td>\n";
            echo "<td>" . date('d/m/Y', strtotime($open_date)) . "</td>\n";
            echo "<td style='text-align: right;'>" . number_format($stored_cash, 2, ',', '.') . "</td>\n";
            echo "<td style='text-align: right;'>" . number_format($real_cash, 2, ',', '.') . "</td>\n";
            echo "<td style='color: " . ($has_problem ? 'red' : 'green') . ";'>" . ($has_problem ? '❌ Incorreto' : '✅ Correto') . "</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        if ($total_problems > 0) {
            echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 20px 0;'>\n";
            echo "<h3>⚠️ Problemas Encontrados</h3>\n";
            echo "<p>Foram encontrados <strong>$total_problems</strong> registros com totais incorretos.</p>\n";
            echo "<p>Clique no botão abaixo para corrigir automaticamente:</p>\n";
            echo "<form method='post' style='margin: 10px 0;'>\n";
            echo "<input type='hidden' name='fix_totals' value='1'>\n";
            echo "<button type='submit' style='background: #ff9800; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>🔧 Corrigir Totais Automaticamente</button>\n";
            echo "</form>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; margin: 20px 0;'>\n";
            echo "<h3>✅ Tudo Correto!</h3>\n";
            echo "<p>Todos os totais estão corretos. Não é necessária nenhuma correção.</p>\n";
            echo "</div>\n";
        }
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000; margin: 20px 0;'>\n";
    echo "<h3>❌ Erro de Conexão</h3>\n";
    echo "<p><strong>Erro:</strong> " . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<p><strong>Links úteis:</strong></p>\n";
echo "<ul>\n";
echo "<li><a href='reports/registers' target='_blank'>Página de Relatórios de Registros</a></li>\n";
echo "<li><a href='debug_register_problem.php' target='_blank'>Debug do Problema</a></li>\n";
echo "<li><a href='test_format_simple.php' target='_blank'>Teste de Formatação</a></li>\n";
echo "</ul>\n";
?>
