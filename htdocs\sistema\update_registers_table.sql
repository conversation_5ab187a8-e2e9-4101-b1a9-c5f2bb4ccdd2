-- Script para adicionar campos de meios de pagamento na tabela tec_registers
-- Execução: Execute este script no banco de dados para adicionar os campos necessários

-- Verificar se os campos já existem antes de adicionar
SET @sql = '';

-- Verificar se o campo total_stripe existe
SELECT COUNT(*) INTO @count 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tec_registers' 
  AND COLUMN_NAME = 'total_stripe';

IF @count = 0 THEN
  SET @sql = CONCAT(@sql, 'ADD COLUMN `total_stripe` decimal(25,2) DEFAULT NULL AFTER `total_cash`, ');
END IF;

-- Verificar se o campo total_CC existe
SELECT COUNT(*) INTO @count 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tec_registers' 
  AND COLUMN_NAME = 'total_CC';

IF @count = 0 THEN
  SET @sql = CONCAT(@sql, 'ADD COLUMN `total_CC` decimal(25,2) DEFAULT NULL AFTER `total_stripe`, ');
END IF;

-- Verificar se o campo total_pix existe
SELECT COUNT(*) INTO @count 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tec_registers' 
  AND COLUMN_NAME = 'total_pix';

IF @count = 0 THEN
  SET @sql = CONCAT(@sql, 'ADD COLUMN `total_pix` decimal(25,2) DEFAULT NULL AFTER `total_CC`, ');
END IF;

-- Remover a vírgula final se houver campos para adicionar
IF LENGTH(@sql) > 0 THEN
  SET @sql = SUBSTRING(@sql, 1, LENGTH(@sql) - 2);
  SET @sql = CONCAT('ALTER TABLE `tec_registers` ', @sql);
  
  PREPARE stmt FROM @sql;
  EXECUTE stmt;
  DEALLOCATE PREPARE stmt;
  
  SELECT 'Campos adicionados com sucesso na tabela tec_registers!' as resultado;
ELSE
  SELECT 'Todos os campos já existem na tabela tec_registers.' as resultado;
END IF;
