<?php
// Script para testar as configurações de formatação monetária

require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=$PDV_HOST;dbname=$PDV_BASE;charset=utf8", $PDV_USUARIO, $PDV_SENHA);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Teste das configurações de formatação monetária</h2>\n";
    
    // Verificar configurações atuais
    $stmt = $pdo->prepare("SELECT decimals, thousands_sep, decimals_sep FROM tec_settings WHERE setting_id = 1");
    $stmt->execute();
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($settings) {
        echo "<h3>Configurações no banco de dados:</h3>\n";
        echo "- Decimais: " . $settings['decimals'] . "<br>\n";
        echo "- Separador de milhares: '" . $settings['thousands_sep'] . "'<br>\n";
        echo "- Separador decimal: '" . $settings['decimals_sep'] . "'<br>\n";
        
        echo "<h3>Teste de formatação JavaScript:</h3>\n";
        echo "<script>\n";
        echo "var Settings = {\n";
        echo "    decimals: " . $settings['decimals'] . ",\n";
        echo "    thousands_sep: '" . $settings['thousands_sep'] . "',\n";
        echo "    decimals_sep: '" . $settings['decimals_sep'] . "'\n";
        echo "};\n";
        echo "</script>\n";
        
        // Simular valores como aparecem no relatório
        $test_values = [31378, 1234.56, 999.99, 10000, 313.78];
        
        echo "<h3>Teste com valores do exemplo:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>Valor Original</th><th>PHP (correto)</th><th>JavaScript (teste)</th></tr>\n";
        
        foreach ($test_values as $value) {
            $php_formatted = number_format($value, $settings['decimals'], $settings['decimals_sep'], $settings['thousands_sep']);
            echo "<tr>\n";
            echo "<td>$value</td>\n";
            echo "<td>$php_formatted</td>\n";
            echo "<td id='js_$value'>Carregando...</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        // JavaScript para testar formatação
        echo "<script src='themes/default/assets/dist/js/common-libs.js'></script>\n";
        echo "<script>\n";
        echo "function formatMoney(x, symbol) {\n";
        echo "    if(!symbol) { symbol = ''; }\n";
        echo "    var x2 = new String(x == '' ? '' : x );\n";
        echo "    x2 = x2.toString();\n";
        echo "    if(x2.indexOf('.')!=-1){\n";
        echo "        var txt = x2.split('.');\n";
        echo "        var decimal = txt[1].substr( 0, 2);\n";
        echo "        var nextDigit = txt[1].substr(2, 1);\n";
        echo "        if (nextDigit != null && nextDigit != undefined) {\n";
        echo "            nextDigit = parseInt(nextDigit);\n";
        echo "            if (nextDigit >= 5) {\n";
        echo "                decimal = parseInt(decimal) + 1;\n";
        echo "                var decimallenght = decimal.toString().length;\n";
        echo "                if(decimallenght==1){ decimal = '0' + decimal; }\n";
        echo "                if(decimal>=100){ txt[0] = parseInt(txt[0]) + 1; decimal = '00';  }\n";
        echo "            }\n";
        echo "        }\n";
        echo "        x = parseFloat(txt[0]+'.'+decimal);\n";
        echo "    }else{\n";
        echo "        x = parseFloat(x);\n";
        echo "    }\n";
        echo "    \n";
        echo "    // Simular accounting.formatMoney\n";
        echo "    var formatted = x.toFixed(Settings.decimals);\n";
        echo "    var parts = formatted.split('.');\n";
        echo "    parts[0] = parts[0].replace(/\\B(?=(\\d{3})+(?!\\d))/g, Settings.thousands_sep);\n";
        echo "    return parts.join(Settings.decimals_sep);\n";
        echo "}\n";
        
        foreach ($test_values as $value) {
            echo "document.getElementById('js_$value').innerHTML = formatMoney($value);\n";
        }
        echo "</script>\n";
        
    } else {
        echo "<span style='color: red;'>Erro: Não foi possível encontrar as configurações.</span><br>\n";
    }
    
} catch (PDOException $e) {
    echo "<span style='color: red;'>Erro de conexão: " . $e->getMessage() . "</span><br>\n";
}
?>
