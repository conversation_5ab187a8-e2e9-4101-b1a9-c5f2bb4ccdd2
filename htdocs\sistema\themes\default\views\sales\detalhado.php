<?php
$v = "?v=1";

    if($this->input->get('customer')){
        $v .= "&customer=".$this->input->get('customer');
    }
    if($this->input->get('user')){
        $v .= "&user=".$this->input->get('user');
    }
    if($this->input->get('start_date')){
        $v .= "&start_date=".$this->input->get('start_date');
    }
    if($this->input->get('end_date')) {
        $v .= "&end_date=".$this->input->get('end_date');
    }
    if($this->input->get('ispaid')) {
        $v .= "&ispaid=".$this->input->get('ispaid');
    }
?>
<script>
	$(document).ready(function () {
		$('#SLData').dataTable({
			"aLengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, '<?= lang('all'); ?>']],
            "aaSorting": [[ 0, "desc" ]],
            "iDisplayLength": <?= $Settings->rows_per_page ?>,
            "aoColumns": [null, {
                "mRender": function(data, type, row) {
                    if (data && data.indexOf('1969') === -1 && data.indexOf('1970') === -1) {
                        return hrld(data);
                    } else {
                        return data;
                    }
                }
            }, null, null, null, null]
		});
	});
</script>
<script type="text/javascript">
    $(document).ready(function(){
        $('#form').hide();
        <?php if($_GET["customer"]!=""){ ?>
            $("#form").slideToggle();
        <?php } ?>
        $('.toggle_form').click(function(){
            $("#form").slideToggle();
            return false;
        });
    });
</script>
<style type="text/css">
.table td { text-align: center; }
.table > tbody > tr:last-child > td { background: #cfcfcf; font-size: 16px; font-weight: 700; ;}
@media print {
    .table th { background: #f5f5f5 !important; border-color: #bbb !important; }
    .table th:nth-child(6), .table td:nth-child(6) { display: none; }
}
</style>
<section class="content">
	<div class="row">

		<div class="col-xs-12">
			<div class="box box-primary">
				<div class="box-header">
					<a href="#" class="btn btn-default btn-sm toggle_form pull-right"><?= lang("show_hide"); ?></a>
					<h3 class="box-title"><?= lang('list_results'); ?></h3>
				</div>
				<div class="box-body">
				<div id="form" class="panel panel-warning">
                    <div class="panel-body">
                        <?=form_open('sales/detalhado', array('method'=>'get')); ?>
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label" for="customer"><?= lang("customer"); ?></label>
                                    <?php
                                    $cu[0] = lang("select")." ".lang("customer");
                                    foreach($customers as $customer){
                                        $cu[$customer->id] = $customer->name;
                                    }
                                    echo form_dropdown('customer', $cu, set_value('customer'), 'class="form-control select2" style="width:100%" id="customer"'); ?>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label" for="start_date"><?= lang("start_date"); ?></label>
                                    <?= form_input('start_date', set_value('start_date'), 'class="form-control datetimepicker" id="start_date"');?>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label" for="end_date"><?= lang("end_date"); ?></label>
                                    <?= form_input('end_date', set_value('end_date'), 'class="form-control datetimepicker" id="end_date"');?>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label" for="user"><?= lang("created_by"); ?></label>
                                    <?php
                                    $us[""] = "";
                                    foreach ($users as $user) {
                                        $us[$user->id] = $user->first_name . " " . $user->last_name;
                                    }
                                    echo form_dropdown('user', $us, (isset($_POST['user']) ? $_POST['user'] : ""), 'class="form-control select2" id="user" data-placeholder="' . lang("select") . " " . lang("user") . '" style="width:100%;"');
                                    ?>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label" for="ispaid"><?= lang("status"); ?></label>
                                    <?php
                                    $ispaid[0] = lang("select")." ".lang("status");
                                    $ispaid[1] = "Pago";
                                    $ispaid[2] = "Não Pago";
                                    $ispaid[3] = "Parcial";
                                    $ispaid[4] = "Não Pago";
                                    echo form_dropdown('ispaid', $ispaid, set_value('ispaid'), 'class="form-control select2" style="width:100%" id="ispaid"'); ?>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <?= lang("tipo", "tipo"); ?>
                                    <?php $tipo = array(""=>"Todos", "contas_a_receber"=>"Contas a receber"); ?>
                                    <?= form_dropdown('tipo', $tipo, set_value('tipo'), 'class="form-control select2" id="tipo" style="width:100%"'); ?>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <button type="submit" class="btn btn-primary"><?= lang("submit"); ?></button>
                            </div>
                        </div>
                        <?= form_close(); ?>
                    </div>
                </div>
                <div class="table-responsive">
                    <table id="SLData" class="table table-striped table-bordered table-condensed table-hover">
                        <thead>
                            <tr class="active">
                                <th class="col-xs-1">ID</th>
                                <th class="col-xs-2">Data</th>
                                <th class="col-xs-2">Usuário</th>
                                <th class="col-xs-4">Produtos</th>
                                <th class="col-xs-2">Forma de pagamento</th>
                                <th class="col-xs-1">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($vendas)): ?>
                                <?php foreach ($vendas as $venda): ?>
                                <tr>
                                    <td><?= $venda->id ?></td>
                                    <td><?= $this->tec->hrld($venda->date) ?></td>
                                    <td><?= $venda->first_name . ' ' . $venda->last_name ?></td>
                                    <td><?= $venda->produtos ?></td>
                                    <td><?= $venda->paid_by ?></td>
                                    <td><?= $this->tec->formatMoney($venda->grand_total) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="6" class="dataTables_empty"><?= lang('no_data_available'); ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <div class="clearfix"></div>
			</div>
		</div>
	</div>
</section>
