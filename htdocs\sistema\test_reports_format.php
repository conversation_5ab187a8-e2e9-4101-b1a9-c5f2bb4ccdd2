<?php
// Teste específico para formatação na página de relatórios

require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=$PDV_HOST;dbname=$PDV_BASE;charset=utf8", $PDV_USUARIO, $PDV_SENHA);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Teste de Formatação - Página de Relatórios</h2>\n";
    
    // Buscar configurações atuais
    $stmt = $pdo->prepare("SELECT * FROM tec_settings WHERE setting_id = 1");
    $stmt->execute();
    $settings = $stmt->fetch(PDO::FETCH_OBJECT);
    
    if ($settings) {
        echo "<h3>Configurações do Sistema:</h3>\n";
        echo "- Decimais: " . $settings->decimals . "<br>\n";
        echo "- Separador de milhares: '" . $settings->thousands_sep . "'<br>\n";
        echo "- Separador decimal: '" . $settings->decimals_sep . "'<br>\n";
        
        // Simular dados como aparecem na página de relatórios
        echo "<h3>Simulação da Página de Relatórios:</h3>\n";
        
        // Buscar alguns dados reais da tabela registers
        $stmt = $pdo->prepare("
            SELECT 
                cash_in_hand as caixa_inicial, 
                (cash_in_hand + COALESCE(total_cash, 0)) as saldo, 
                COALESCE(total_stripe, 0) as debito, 
                COALESCE(total_CC, 0) as credito, 
                COALESCE(total_cash, 0) as dinheiro, 
                COALESCE(total_pix, 0) as pix 
            FROM tec_registers 
            ORDER BY id DESC 
            LIMIT 5
        ");
        $stmt->execute();
        $registers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($registers) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
            echo "<tr style='background: #f0f0f0;'>\n";
            echo "<th>Caixa Inicial</th><th>Saldo</th><th>Débito</th><th>Crédito</th><th>Dinheiro</th><th>PIX</th>\n";
            echo "</tr>\n";
            
            foreach ($registers as $register) {
                echo "<tr>\n";
                echo "<td>" . number_format($register['caixa_inicial'], $settings->decimals, $settings->decimals_sep, $settings->thousands_sep) . "</td>\n";
                echo "<td>" . number_format($register['saldo'], $settings->decimals, $settings->decimals_sep, $settings->thousands_sep) . "</td>\n";
                echo "<td>" . number_format($register['debito'], $settings->decimals, $settings->decimals_sep, $settings->thousands_sep) . "</td>\n";
                echo "<td>" . number_format($register['credito'], $settings->decimals, $settings->decimals_sep, $settings->thousands_sep) . "</td>\n";
                echo "<td>" . number_format($register['dinheiro'], $settings->decimals, $settings->decimals_sep, $settings->thousands_sep) . "</td>\n";
                echo "<td>" . number_format($register['pix'], $settings->decimals, $settings->decimals_sep, $settings->thousands_sep) . "</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p>Nenhum registro encontrado na tabela tec_registers.</p>\n";
        }
        
        // Teste JavaScript
        echo "<h3>Teste JavaScript (como na página real):</h3>\n";
        echo "<div id='test-results'></div>\n";
        
        echo "<script src='themes/default/assets/dist/js/common-libs.js'></script>\n";
        echo "<script>\n";
        echo "// Configurações do sistema\n";
        echo "var Settings = {\n";
        echo "    decimals: " . $settings->decimals . ",\n";
        echo "    thousands_sep: '" . $settings->thousands_sep . "',\n";
        echo "    decimals_sep: '" . $settings->decimals_sep . "'\n";
        echo "};\n\n";
        
        echo "// Função formatMoney do sistema\n";
        echo "function formatMoney(x, symbol) {\n";
        echo "    if(!symbol) { symbol = ''; }\n";
        echo "    var x2 = new String(x == '' ? '' : x );\n";
        echo "    x2 = x2.toString();\n";
        echo "    if(x2.indexOf('.')!=-1){\n";
        echo "        var txt = x2.split('.');\n";
        echo "        var decimal = txt[1].substr( 0, 2);\n";
        echo "        var nextDigit = txt[1].substr(2, 1);\n";
        echo "        if (nextDigit != null && nextDigit != undefined) {\n";
        echo "            nextDigit = parseInt(nextDigit);\n";
        echo "            if (nextDigit >= 5) {\n";
        echo "                decimal = parseInt(decimal) + 1;\n";
        echo "                var decimallenght = decimal.toString().length;\n";
        echo "                if(decimallenght==1){ decimal = '0' + decimal; }\n";
        echo "                if(decimal>=100){ txt[0] = parseInt(txt[0]) + 1; decimal = '00';  }\n";
        echo "            }\n";
        echo "        }\n";
        echo "        x = parseFloat(txt[0]+'.'+decimal);\n";
        echo "    }else{\n";
        echo "        x = parseFloat(x);\n";
        echo "    }\n";
        echo "    return accounting.formatMoney(x, symbol, Settings.decimals, Settings.thousands_sep == 0 ? ' ' : Settings.thousands_sep, Settings.decimals_sep, '%s%v');\n";
        echo "}\n\n";
        
        echo "// Função currencyFormat do sistema\n";
        echo "function currencyFormat(x) {\n";
        echo "    if (x != null) {\n";
        echo "        return '<div class=\"text-right\">'+formatMoney(x)+'</div>';\n";
        echo "    } else {\n";
        echo "        return '<div class=\"text-right\">0</div>';\n";
        echo "    }\n";
        echo "}\n\n";
        
        echo "// Testar valores\n";
        echo "var testValues = [31378, 1234.56, 999.99, 10000, 313.78];\n";
        echo "var html = '<table border=\"1\" style=\"border-collapse: collapse; margin: 10px 0;\"><tr><th>Valor</th><th>formatMoney()</th><th>currencyFormat()</th></tr>';\n";
        echo "for (var i = 0; i < testValues.length; i++) {\n";
        echo "    var value = testValues[i];\n";
        echo "    html += '<tr>';\n";
        echo "    html += '<td>' + value + '</td>';\n";
        echo "    html += '<td>' + formatMoney(value) + '</td>';\n";
        echo "    html += '<td>' + currencyFormat(value) + '</td>';\n";
        echo "    html += '</tr>';\n";
        echo "}\n";
        echo "html += '</table>';\n";
        echo "document.getElementById('test-results').innerHTML = html;\n";
        echo "</script>\n";
        
    } else {
        echo "<span style='color: red;'>Erro: Não foi possível encontrar as configurações.</span><br>\n";
    }
    
} catch (PDOException $e) {
    echo "<span style='color: red;'>Erro de conexão: " . $e->getMessage() . "</span><br>\n";
}

echo "<br><h3>Links úteis:</h3>\n";
echo "<ul>\n";
echo "<li><a href='reports/registers' target='_blank'>Página de Relatórios de Registros</a></li>\n";
echo "<li><a href='fix_currency_format.php' target='_blank'>Script de Correção</a></li>\n";
echo "</ul>\n";
?>
