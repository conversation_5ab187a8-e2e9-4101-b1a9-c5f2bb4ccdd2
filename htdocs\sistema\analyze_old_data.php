<?php
// Script para análise detalhada de dados antigos
// Identifica padrões e problemas específicos em registros históricos

require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=$PDV_HOST;dbname=$PDV_BASE;charset=utf8", $PDV_USUARIO, $PDV_SENHA);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔍 Análise Detalhada de Dados Antigos</h2>\n";
    echo "<p><em>Identificação de padrões e problemas em registros históricos</em></p>\n";
    
    // 1. Análise geral dos dados
    echo "<h3>📊 Análise Geral dos Dados</h3>\n";
    
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_registers,
            COUNT(CASE WHEN status = 'open' THEN 1 END) as open_registers,
            COUNT(CASE WHEN status = 'close' THEN 1 END) as closed_registers,
            COUNT(CASE WHEN status = 'close' AND total_cash IS NULL THEN 1 END) as missing_cash,
            COUNT(CASE WHEN status = 'close' AND total_stripe IS NULL THEN 1 END) as missing_stripe,
            COUNT(CASE WHEN status = 'close' AND total_CC IS NULL THEN 1 END) as missing_cc,
            COUNT(CASE WHEN status = 'close' AND total_pix IS NULL THEN 1 END) as missing_pix,
            MIN(date) as oldest_register,
            MAX(date) as newest_register,
            COUNT(DISTINCT user_id) as unique_users
        FROM tec_registers
    ");
    $general_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr style='background: #f0f0f0;'><th>Métrica</th><th>Valor</th></tr>\n";
    echo "<tr><td>Total de Registros</td><td>" . $general_stats['total_registers'] . "</td></tr>\n";
    echo "<tr><td>Registros Abertos</td><td>" . $general_stats['open_registers'] . "</td></tr>\n";
    echo "<tr><td>Registros Fechados</td><td>" . $general_stats['closed_registers'] . "</td></tr>\n";
    echo "<tr><td>Usuários Únicos</td><td>" . $general_stats['unique_users'] . "</td></tr>\n";
    echo "<tr><td>Período</td><td>" . date('d/m/Y', strtotime($general_stats['oldest_register'])) . " até " . date('d/m/Y', strtotime($general_stats['newest_register'])) . "</td></tr>\n";
    echo "<tr style='background: #ffeeee;'><td>Faltam Totais Dinheiro</td><td>" . $general_stats['missing_cash'] . "</td></tr>\n";
    echo "<tr style='background: #ffeeee;'><td>Faltam Totais Débito</td><td>" . $general_stats['missing_stripe'] . "</td></tr>\n";
    echo "<tr style='background: #ffeeee;'><td>Faltam Totais Crédito</td><td>" . $general_stats['missing_cc'] . "</td></tr>\n";
    echo "<tr style='background: #ffeeee;'><td>Faltam Totais PIX</td><td>" . $general_stats['missing_pix'] . "</td></tr>\n";
    echo "</table>\n";
    
    // 2. Análise por período
    echo "<h3>📅 Análise por Período</h3>\n";
    
    $stmt = $pdo->query("
        SELECT 
            YEAR(date) as ano,
            MONTH(date) as mes,
            COUNT(*) as total_registros,
            COUNT(CASE WHEN status = 'close' THEN 1 END) as fechados,
            COUNT(CASE WHEN status = 'close' AND total_cash IS NULL THEN 1 END) as sem_totais,
            AVG(CASE WHEN status = 'close' AND total_cash IS NOT NULL THEN total_cash END) as media_dinheiro
        FROM tec_registers 
        GROUP BY YEAR(date), MONTH(date)
        ORDER BY ano DESC, mes DESC
        LIMIT 12
    ");
    $period_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($period_stats) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f0f0f0;'>\n";
        echo "<th>Período</th><th>Total Registros</th><th>Fechados</th><th>Sem Totais</th><th>Média Dinheiro</th>\n";
        echo "</tr>\n";
        
        foreach ($period_stats as $period) {
            $periodo = sprintf("%02d/%d", $period['mes'], $period['ano']);
            $media_dinheiro = $period['media_dinheiro'] ? number_format($period['media_dinheiro'], 2, ',', '.') : 'N/A';
            $problema = $period['sem_totais'] > 0;
            
            echo "<tr style='background: " . ($problema ? '#ffeeee' : '#eeffee') . ";'>\n";
            echo "<td>$periodo</td>\n";
            echo "<td>" . $period['total_registros'] . "</td>\n";
            echo "<td>" . $period['fechados'] . "</td>\n";
            echo "<td>" . ($problema ? "❌ " . $period['sem_totais'] : "✅ 0") . "</td>\n";
            echo "<td>R$ $media_dinheiro</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // 3. Análise por usuário
    echo "<h3>👥 Análise por Usuário</h3>\n";
    
    $stmt = $pdo->query("
        SELECT 
            r.user_id,
            CONCAT(u.first_name, ' ', u.last_name) as nome_usuario,
            COUNT(*) as total_registros,
            COUNT(CASE WHEN r.status = 'close' THEN 1 END) as fechados,
            COUNT(CASE WHEN r.status = 'close' AND r.total_cash IS NULL THEN 1 END) as sem_totais,
            MIN(r.date) as primeiro_registro,
            MAX(r.date) as ultimo_registro
        FROM tec_registers r
        LEFT JOIN tec_users u ON r.user_id = u.id
        GROUP BY r.user_id
        ORDER BY sem_totais DESC, total_registros DESC
    ");
    $user_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($user_stats) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>\n";
        echo "<tr style='background: #f0f0f0;'>\n";
        echo "<th>ID</th><th>Nome</th><th>Total</th><th>Fechados</th><th>Sem Totais</th><th>Primeiro</th><th>Último</th>\n";
        echo "</tr>\n";
        
        foreach ($user_stats as $user) {
            $problema = $user['sem_totais'] > 0;
            $nome = $user['nome_usuario'] ?: 'Usuário #' . $user['user_id'];
            
            echo "<tr style='background: " . ($problema ? '#ffeeee' : '#ffffff') . ";'>\n";
            echo "<td>" . $user['user_id'] . "</td>\n";
            echo "<td>$nome</td>\n";
            echo "<td>" . $user['total_registros'] . "</td>\n";
            echo "<td>" . $user['fechados'] . "</td>\n";
            echo "<td>" . ($problema ? "❌ " . $user['sem_totais'] : "✅ 0") . "</td>\n";
            echo "<td>" . date('d/m/Y', strtotime($user['primeiro_registro'])) . "</td>\n";
            echo "<td>" . date('d/m/Y', strtotime($user['ultimo_registro'])) . "</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // 4. Identificar registros órfãos (sem vendas correspondentes)
    echo "<h3>🔍 Registros Órfãos (sem vendas correspondentes)</h3>\n";
    
    $stmt = $pdo->query("
        SELECT 
            r.id,
            r.date as abertura,
            r.closed_at as fechamento,
            r.user_id,
            r.cash_in_hand,
            r.total_cash,
            (SELECT COUNT(*) FROM tec_sales s WHERE s.date > r.date AND s.date <= r.closed_at AND s.created_by = r.user_id) as vendas_periodo,
            (SELECT COUNT(*) FROM tec_payments p WHERE p.date > r.date AND p.date <= r.closed_at AND p.created_by = r.user_id) as pagamentos_periodo
        FROM tec_registers r
        WHERE r.status = 'close' 
        AND r.closed_at IS NOT NULL
        HAVING vendas_periodo = 0 AND pagamentos_periodo = 0
        ORDER BY r.date DESC
        LIMIT 10
    ");
    $orphan_registers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($orphan_registers) {
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>\n";
        echo "<h4>⚠️ Registros Órfãos Encontrados</h4>\n";
        echo "<p>Estes registros não têm vendas ou pagamentos correspondentes no período:</p>\n";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>\n";
        echo "<tr style='background: #f0f0f0;'>\n";
        echo "<th>ID</th><th>Abertura</th><th>Fechamento</th><th>Usuário</th><th>Caixa Inicial</th><th>Total Cash</th>\n";
        echo "</tr>\n";
        
        foreach ($orphan_registers as $orphan) {
            echo "<tr>\n";
            echo "<td>" . $orphan['id'] . "</td>\n";
            echo "<td>" . date('d/m/Y H:i', strtotime($orphan['abertura'])) . "</td>\n";
            echo "<td>" . date('d/m/Y H:i', strtotime($orphan['fechamento'])) . "</td>\n";
            echo "<td>" . $orphan['user_id'] . "</td>\n";
            echo "<td style='text-align: right;'>" . number_format($orphan['cash_in_hand'], 2, ',', '.') . "</td>\n";
            echo "<td style='text-align: right;'>" . ($orphan['total_cash'] ? number_format($orphan['total_cash'], 2, ',', '.') : 'NULL') . "</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
        echo "</div>\n";
    } else {
        echo "<p style='color: green;'>✅ Nenhum registro órfão encontrado.</p>\n";
    }
    
    // 5. Verificar inconsistências nos totais
    echo "<h3>⚖️ Verificação de Inconsistências</h3>\n";
    
    $stmt = $pdo->query("
        SELECT 
            r.id,
            r.date,
            r.closed_at,
            r.user_id,
            r.total_cash as stored_cash,
            (SELECT COALESCE(SUM(p.amount), 0) FROM tec_payments p 
             WHERE p.date > r.date AND p.date <= r.closed_at 
             AND p.created_by = r.user_id AND p.paid_by = 'cash' 
             AND p.type != 'returned') as calculated_cash,
            ABS(r.total_cash - (SELECT COALESCE(SUM(p.amount), 0) FROM tec_payments p 
                               WHERE p.date > r.date AND p.date <= r.closed_at 
                               AND p.created_by = r.user_id AND p.paid_by = 'cash' 
                               AND p.type != 'returned')) as diferenca
        FROM tec_registers r
        WHERE r.status = 'close' 
        AND r.closed_at IS NOT NULL
        AND r.total_cash IS NOT NULL
        HAVING diferenca > 0.01
        ORDER BY diferenca DESC
        LIMIT 10
    ");
    $inconsistencies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($inconsistencies) {
        echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000; margin: 10px 0;'>\n";
        echo "<h4>❌ Inconsistências Encontradas</h4>\n";
        echo "<p>Registros onde o total armazenado não confere com o calculado:</p>\n";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>\n";
        echo "<tr style='background: #f0f0f0;'>\n";
        echo "<th>ID</th><th>Data</th><th>Usuário</th><th>Armazenado</th><th>Calculado</th><th>Diferença</th>\n";
        echo "</tr>\n";
        
        foreach ($inconsistencies as $inc) {
            echo "<tr>\n";
            echo "<td>" . $inc['id'] . "</td>\n";
            echo "<td>" . date('d/m/Y', strtotime($inc['date'])) . "</td>\n";
            echo "<td>" . $inc['user_id'] . "</td>\n";
            echo "<td style='text-align: right;'>" . number_format($inc['stored_cash'], 2, ',', '.') . "</td>\n";
            echo "<td style='text-align: right;'>" . number_format($inc['calculated_cash'], 2, ',', '.') . "</td>\n";
            echo "<td style='text-align: right; color: red;'>" . number_format($inc['diferenca'], 2, ',', '.') . "</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
        echo "</div>\n";
    } else {
        echo "<p style='color: green;'>✅ Nenhuma inconsistência significativa encontrada nos totais existentes.</p>\n";
    }
    
    // 6. Recomendações
    echo "<h3>💡 Recomendações</h3>\n";
    
    $total_problems = $general_stats['missing_cash'] + $general_stats['missing_stripe'] + 
                     $general_stats['missing_cc'] + $general_stats['missing_pix'];
    
    echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; margin: 10px 0;'>\n";
    
    if ($total_problems > 0) {
        echo "<h4>🔧 Ações Recomendadas:</h4>\n";
        echo "<ol>\n";
        echo "<li><strong>Execute o script de correção:</strong> <a href='fix_old_reports.php' target='_blank'>fix_old_reports.php</a></li>\n";
        echo "<li><strong>Verifique registros órfãos:</strong> Considere se devem ser mantidos ou removidos</li>\n";
        if (!empty($inconsistencies)) {
            echo "<li><strong>Corrija inconsistências:</strong> Execute recálculo para registros com diferenças</li>\n";
        }
        echo "<li><strong>Monitore novos registros:</strong> Certifique-se de que o processo de fechamento está funcionando corretamente</li>\n";
        echo "</ol>\n";
    } else {
        echo "<h4>✅ Status Excelente!</h4>\n";
        echo "<p>Todos os registros estão com dados consistentes. Nenhuma ação necessária.</p>\n";
    }
    
    echo "</div>\n";
    
} catch (PDOException $e) {
    echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000; margin: 20px 0;'>\n";
    echo "<h3>❌ Erro de Conexão</h3>\n";
    echo "<p><strong>Erro:</strong> " . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<p><strong>Scripts relacionados:</strong></p>\n";
echo "<ul>\n";
echo "<li><a href='fix_old_reports.php' target='_blank'>🔧 Correção de Relatórios Antigos</a></li>\n";
echo "<li><a href='fix_register_totals.php' target='_blank'>🔧 Correção de Totais (Registros Recentes)</a></li>\n";
echo "<li><a href='debug_register_problem.php' target='_blank'>🔍 Debug do Problema</a></li>\n";
echo "<li><a href='reports/registers' target='_blank'>📊 Página de Relatórios</a></li>\n";
echo "</ul>\n";
?>
