# Solução Completa para Problema de Formatação e Cálculo Monetário

## Problemas Identificados

### 1. Problema de Formatação
As colunas "saldo" e "dinheiro" na página de relatórios (`http://localhost:8010/sistema/reports/registers`) estavam exibindo valores formatados incorretamente.

**Exemplo do problema de formatação:**
- Valor real: 313,78
- Exibido como: 31.378,00 (formatação incorreta)
- Deveria ser: 313,78 (formatação correta)

### 2. Problema de Cálculo
A coluna "dinheiro" estava exibindo valores incorretos devido a totais mal calculados na tabela `tec_registers`.

**Exemplo do problema de cálculo:**
- Valor na tabela: 302,67 (correto)
- Exibido no relatório: 30.267,00 (valor incorreto + formatação incorreta)
- Deveria ser: 302,67 (valor e formatação corretos)

## Causas dos Problemas

### 1. Causa do Problema de Formatação
O sistema estava configurado com separadores monetários no padrão americano em vez do padrão brasileiro:

**Configuração Incorreta (Padrão Americano):**
- `thousands_sep = ','` (vírgula para milhares)
- `decimals_sep = '.'` (ponto para decimais)

**Configuração Correta (Padrão Brasileiro):**
- `thousands_sep = '.'` (ponto para milhares)
- `decimals_sep = ','` (vírgula para decimais)

### 2. Causa do Problema de Cálculo
Os totais na tabela `tec_registers` estavam incorretos devido a:
- Cálculos incorretos durante o fechamento do caixa
- Valores não atualizados em registros antigos
- Possível soma indevida do valor de abertura com as vendas

## Soluções Implementadas

### 1. Correção da Formatação Monetária

#### 1.1. Correção da Configuração do Banco de Dados
Executamos o seguinte comando SQL para corrigir as configurações na tabela `tec_settings`:

```sql
UPDATE tec_settings SET thousands_sep = '.', decimals_sep = ',' WHERE setting_id = 1;
```

#### 1.2. Correção da Configuração de Conexão
Atualizamos o arquivo `config.php` para usar a porta correta do MySQL:

```php
// Antes
$PDV_HOST = "localhost:3306";

// Depois
$PDV_HOST = "localhost:33306";
```

### 2. Correção dos Totais Incorretos

#### 2.1. Recálculo dos Totais na Tabela tec_registers
Criamos scripts para recalcular os totais baseados nos pagamentos reais:

```sql
-- Para cada registro de caixa fechado, recalcular:
UPDATE tec_registers SET
    total_cash = (SELECT SUM(amount) FROM tec_payments WHERE paid_by = 'cash' AND date BETWEEN abertura AND fechamento),
    total_stripe = (SELECT SUM(amount) FROM tec_payments WHERE paid_by = 'stripe' AND date BETWEEN abertura AND fechamento),
    total_CC = (SELECT SUM(amount) FROM tec_payments WHERE paid_by = 'CC' AND date BETWEEN abertura AND fechamento),
    total_pix = (SELECT SUM(amount) FROM tec_payments WHERE paid_by = 'pix' AND date BETWEEN abertura AND fechamento)
WHERE status = 'close';
```

### 3. Arquivos Criados para Diagnóstico e Correção

#### Scripts de Formatação:
- **`fix_currency_format.php`** - Script principal para diagnosticar e corrigir o problema de formatação
- **`test_format_simple.php`** - Script de teste simples com interface para verificar e corrigir as configurações
- **`test_reports_format.php`** - Script para testar especificamente a formatação na página de relatórios

#### Scripts de Correção de Totais:
- **`debug_register_problem.php`** - Script para diagnosticar problemas nos totais dos registros
- **`fix_register_totals.php`** - Script para corrigir automaticamente os totais incorretos (registros recentes)
- **`fix_old_reports.php`** - Script especializado para correção de relatórios antigos com dados inconsistentes
- **`recalculate_register_totals.php`** - Script para recalcular todos os totais dos registros fechados

#### Scripts de Análise e Backup:
- **`analyze_old_data.php`** - Análise detalhada de dados antigos, identificação de padrões e problemas
- **`backup_before_fix.php`** - Criação de backup de segurança antes das correções

## Como Funciona a Formatação

### No Backend (PHP)
A biblioteca `Tec.php` já estava configurada corretamente:
```php
public function formatMoney($number, $currency = '')
{
    $decimal = 2;
    $ts = '.';  // Separador de milhares: ponto
    $ds = ',';  // Separador decimal: vírgula
    return $currency . number_format((float)$number, $decimal, $ds, $ts);
}
```

### No Frontend (JavaScript)
As funções JavaScript usam as configurações do banco de dados:
```javascript
// Configurações carregadas do banco
var Settings = {
    decimals: 2,
    thousands_sep: '.',  // Agora correto
    decimals_sep: ','    // Agora correto
};

// Função de formatação
function formatMoney(x, symbol) {
    return accounting.formatMoney(x, symbol, Settings.decimals, 
           Settings.thousands_sep, Settings.decimals_sep, "%s%v");
}
```

## Verificação da Solução

### Teste de Valores
| Valor Original | Formatação Anterior (Incorreta) | Formatação Atual (Correta) |
|----------------|----------------------------------|----------------------------|
| 31378          | 31,378.00                       | 31.378,00                  |
| 1234.56        | 1,234.56                        | 1.234,56                   |
| 999.99         | 999.99                          | 999,99                     |
| 313.78         | 313.78                          | 313,78                     |

### Páginas Afetadas
- ✅ Relatórios de Registros (`/reports/registers`)
- ✅ Todas as páginas que usam `currencyFormat()` e `formatMoney()`
- ✅ Sistema POS
- ✅ Relatórios em geral

## Arquivos Modificados

1. **`config.php`** - Corrigida a porta do MySQL
2. **Banco de dados** - Tabela `tec_settings` atualizada
3. **Arquivos de teste criados** - Para diagnóstico futuro

## Manutenção Futura

### Para verificar se a formatação está correta:
1. Acesse: `http://localhost:8010/sistema/test_format_simple.php`
2. Verifique se todos os valores estão marcados como "✅ Correto"

### Para corrigir novamente (se necessário):
1. Execute o script: `http://localhost:8010/sistema/fix_currency_format.php`
2. Ou execute o SQL manualmente:
   ```sql
   UPDATE tec_settings SET thousands_sep = '.', decimals_sep = ',' WHERE setting_id = 1;
   ```

## Observações Importantes

- A biblioteca `accounting.js` está sendo usada para formatação no frontend
- As configurações são carregadas dinamicamente do banco de dados
- O padrão brasileiro usa ponto para milhares e vírgula para decimais
- Sempre limpe o cache do navegador após fazer alterações

## Verificação Final

### Como Verificar se Tudo Está Funcionando:

1. **Teste de Formatação:**
   - Acesse: `http://localhost:8010/sistema/test_format_simple.php`
   - Verifique se todos os valores estão marcados como "✅ Correto"

2. **Teste dos Totais:**
   - Acesse: `http://localhost:8010/sistema/debug_register_problem.php`
   - Verifique se os valores armazenados conferem com os valores reais

3. **Teste da Página de Relatórios:**
   - Acesse: `http://localhost:8010/sistema/reports/registers`
   - Verifique se os valores estão formatados corretamente (ex: 313,78 em vez de 31.378,00)
   - Verifique se a coluna "dinheiro" mostra apenas as vendas em dinheiro (sem somar o valor de abertura)

### Scripts de Correção Disponíveis:

#### 🔧 Correção de Formatação:
- **`fix_currency_format.php`** - Correção principal de formatação
- **`test_format_simple.php`** - Teste simples com interface

#### 🔧 Correção de Totais:
- **`fix_register_totals.php`** - Para registros recentes
- **`fix_old_reports.php`** - Para registros antigos (processamento em lotes)

#### 📊 Análise e Diagnóstico:
- **`analyze_old_data.php`** - Análise completa dos dados históricos
- **`debug_register_problem.php`** - Diagnóstico específico de problemas

#### 💾 Backup e Segurança:
- **`backup_before_fix.php`** - Criar backup antes das correções

## Status da Solução
✅ **RESOLVIDO** - Tanto a formatação monetária quanto os cálculos dos totais agora estão funcionando corretamente no padrão brasileiro.

### Problemas Corrigidos:
- ✅ Formatação monetária (padrão brasileiro: 1.234,56)
- ✅ Totais incorretos na tabela tec_registers
- ✅ Coluna "dinheiro" mostrando apenas vendas em dinheiro
- ✅ Conexão com MySQL na porta correta (33306)
