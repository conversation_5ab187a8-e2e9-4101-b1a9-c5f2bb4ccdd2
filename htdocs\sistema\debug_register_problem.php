<?php
// Debug do problema de cálculo na coluna dinheiro

require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=$PDV_HOST;dbname=$PDV_BASE;charset=utf8", $PDV_USUARIO, $PDV_SENHA);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔍 Debug do Problema na Coluna Dinheiro</h2>\n";
    
    // Buscar dados da tabela registers
    $stmt = $pdo->prepare("
        SELECT 
            id,
            date as abertura,
            closed_at as fechamento,
            cash_in_hand as caixa_inicial,
            total_cash as total_vendas_dinheiro,
            (cash_in_hand + COALESCE(total_cash, 0)) as saldo_calculado,
            COALESCE(total_stripe, 0) as debito,
            COALESCE(total_CC, 0) as credito,
            COALESCE(total_pix, 0) as pix,
            status
        FROM tec_registers 
        WHERE status = 'close'
        ORDER BY id DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $registers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($registers) {
        echo "<h3>📊 Dados da Tabela tec_registers:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>\n";
        echo "<tr style='background: #f0f0f0;'>\n";
        echo "<th>ID</th><th>Abertura</th><th>Caixa Inicial</th><th>Vendas Dinheiro</th><th>Saldo (Atual)</th><th>Débito</th><th>Crédito</th><th>PIX</th>\n";
        echo "</tr>\n";
        
        foreach ($registers as $register) {
            echo "<tr>\n";
            echo "<td>" . $register['id'] . "</td>\n";
            echo "<td>" . date('d/m/Y H:i', strtotime($register['abertura'])) . "</td>\n";
            echo "<td style='text-align: right;'>" . number_format($register['caixa_inicial'], 2, ',', '.') . "</td>\n";
            echo "<td style='text-align: right; color: blue; font-weight: bold;'>" . number_format($register['total_vendas_dinheiro'], 2, ',', '.') . "</td>\n";
            echo "<td style='text-align: right;'>" . number_format($register['saldo_calculado'], 2, ',', '.') . "</td>\n";
            echo "<td style='text-align: right;'>" . number_format($register['debito'], 2, ',', '.') . "</td>\n";
            echo "<td style='text-align: right;'>" . number_format($register['credito'], 2, ',', '.') . "</td>\n";
            echo "<td style='text-align: right;'>" . number_format($register['pix'], 2, ',', '.') . "</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        echo "<h3>🔍 Análise do Problema:</h3>\n";
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>\n";
        echo "<h4>Problema Identificado:</h4>\n";
        echo "<p><strong>Na página de relatórios:</strong></p>\n";
        echo "<ul>\n";
        echo "<li><strong>Coluna 'Saldo':</strong> cash_in_hand + total_cash (CORRETO)</li>\n";
        echo "<li><strong>Coluna 'Dinheiro':</strong> total_cash (CORRETO - apenas vendas em dinheiro)</li>\n";
        echo "</ul>\n";
        echo "<p><strong>O problema não está na consulta SQL, mas sim:</strong></p>\n";
        echo "<ol>\n";
        echo "<li><strong>Formatação:</strong> Os valores estão sendo formatados incorretamente (já corrigido)</li>\n";
        echo "<li><strong>Dados incorretos:</strong> O campo total_cash pode estar com valores errados</li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        
        // Verificar a consulta exata do relatório
        echo "<h3>📋 Consulta SQL do Relatório (Reports.php linha 280):</h3>\n";
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ccc;'>\n";
        echo "SELECT \n";
        echo "  cash_in_hand as caixa_inicial,\n";
        echo "  (cash_in_hand + COALESCE(total_cash, 0)) as saldo,\n";
        echo "  COALESCE(total_cash, 0) as dinheiro,  -- APENAS vendas em dinheiro\n";
        echo "  COALESCE(total_stripe, 0) as debito,\n";
        echo "  COALESCE(total_CC, 0) as credito,\n";
        echo "  COALESCE(total_pix, 0) as pix\n";
        echo "FROM tec_registers\n";
        echo "</pre>\n";
        
        echo "<h3>✅ Verificação dos Dados:</h3>\n";
        
        // Para cada registro, verificar se os totais estão corretos
        foreach ($registers as $register) {
            $register_id = $register['id'];
            $open_date = $register['abertura'];
            $close_date = $register['fechamento'];
            
            if ($close_date) {
                echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>\n";
                echo "<h4>🔍 Registro #$register_id</h4>\n";
                echo "<p><strong>Período:</strong> " . date('d/m/Y H:i', strtotime($open_date)) . " até " . date('d/m/Y H:i', strtotime($close_date)) . "</p>\n";
                
                // Verificar vendas em dinheiro no período
                $stmt_check = $pdo->prepare("
                    SELECT 
                        COUNT(*) as qtd_vendas,
                        SUM(COALESCE(amount, 0)) as total_real_dinheiro
                    FROM tec_payments 
                    WHERE date > ? 
                    AND date <= ?
                    AND paid_by = 'cash'
                ");
                $stmt_check->execute([$open_date, $close_date]);
                $real_data = $stmt_check->fetch(PDO::FETCH_ASSOC);
                
                $stored_value = $register['total_vendas_dinheiro'];
                $real_value = $real_data['total_real_dinheiro'];
                $is_correct = abs($stored_value - $real_value) < 0.01;
                
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
                echo "<tr><th>Campo</th><th>Valor Armazenado</th><th>Valor Real (Calculado)</th><th>Status</th></tr>\n";
                echo "<tr>\n";
                echo "<td>Vendas em Dinheiro</td>\n";
                echo "<td style='text-align: right;'>" . number_format($stored_value, 2, ',', '.') . "</td>\n";
                echo "<td style='text-align: right;'>" . number_format($real_value, 2, ',', '.') . "</td>\n";
                echo "<td style='color: " . ($is_correct ? 'green' : 'red') . ";'>" . ($is_correct ? '✅ Correto' : '❌ Incorreto') . "</td>\n";
                echo "</tr>\n";
                echo "</table>\n";
                
                if (!$is_correct) {
                    echo "<p style='color: red;'><strong>⚠️ Problema encontrado!</strong> O valor armazenado não confere com as vendas reais.</p>\n";
                    echo "<p><strong>Diferença:</strong> " . number_format(abs($stored_value - $real_value), 2, ',', '.') . "</p>\n";
                }
                
                echo "</div>\n";
            }
        }
        
        echo "<h3>🔧 Soluções Possíveis:</h3>\n";
        echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; margin: 10px 0;'>\n";
        echo "<ol>\n";
        echo "<li><strong>Recalcular totais:</strong> Execute o script de recálculo para corrigir os valores armazenados</li>\n";
        echo "<li><strong>Verificar processo de fechamento:</strong> Garantir que os totais sejam calculados corretamente no fechamento</li>\n";
        echo "<li><strong>Corrigir formatação:</strong> Já foi corrigida a formatação monetária</li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        
    } else {
        echo "<p style='color: red;'>❌ Nenhum registro de caixa fechado encontrado.</p>\n";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000; margin: 20px 0;'>\n";
    echo "<h3>❌ Erro de Conexão</h3>\n";
    echo "<p><strong>Erro:</strong> " . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<p><strong>Links úteis:</strong></p>\n";
echo "<ul>\n";
echo "<li><a href='reports/registers' target='_blank'>Página de Relatórios de Registros</a></li>\n";
echo "<li><a href='recalculate_register_totals.php' target='_blank'>Recalcular Totais dos Registros</a></li>\n";
echo "</ul>\n";
?>
