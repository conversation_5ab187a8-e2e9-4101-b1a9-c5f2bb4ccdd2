<?php
/**
 * Script para recalcular retroativamente os totais de meios de pagamento 
 * para caixas fechados que não possuem esses valores
 */

// Configurações do banco de dados - ajuste conforme necessário
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sis_nfe_pdv'; // Ajuste o nome do banco

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<h2>Recalculando totais de meios de pagamento para caixas antigos...</h2>\n";

    // Verificar se os campos existem na tabela
    $fields_exist = [];
    $required_fields = ['total_stripe', 'total_CC', 'total_pix'];

    foreach ($required_fields as $field) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'tec_registers' AND COLUMN_NAME = ?");
        $stmt->execute([$database, $field]);
        $fields_exist[$field] = $stmt->fetchColumn() > 0;
    }

    $missing_fields = array_filter($fields_exist, function($exists) { return !$exists; });

    if (!empty($missing_fields)) {
        echo "<div style='color: red; border: 1px solid red; padding: 10px; margin: 10px 0;'>\n";
        echo "<strong>⚠ ATENÇÃO:</strong> Os seguintes campos não existem na tabela tec_registers:<br>\n";
        foreach ($missing_fields as $field => $exists) {
            if (!$exists) echo "• $field<br>\n";
        }
        echo "<br>Execute primeiro o script <strong>update_registers_table.php</strong> para adicionar os campos necessários.\n";
        echo "</div>\n";
        exit;
    }

    echo "<p style='color: green;'>✓ Todos os campos necessários existem na tabela.</p>\n";

    // Buscar todos os registros fechados que não têm os totais calculados
    $stmt = $pdo->query("
        SELECT id, date, closed_at, user_id, total_cash, total_stripe, total_CC, total_pix
        FROM tec_registers 
        WHERE status = 'close' 
        AND closed_at IS NOT NULL
        ORDER BY date ASC
    ");
    
    $registers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $total_registers = count($registers);
    $updated_count = 0;
    $skipped_count = 0;
    
    echo "<p>Encontrados <strong>$total_registers</strong> registros de caixa fechados.</p>\n";
    echo "<div style='max-height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px;'>\n";
    
    foreach ($registers as $register) {
        $register_id = $register['id'];
        $open_date = $register['date'];
        $close_date = $register['closed_at'];
        $user_id = $register['user_id'];
        
        // Verificar se já tem os totais calculados
        $has_totals = !is_null($register['total_stripe']) || 
                     !is_null($register['total_CC']) || 
                     !is_null($register['total_pix']);
        
        if ($has_totals) {
            echo "<span style='color: #666;'>• Registro #$register_id (usuário $user_id) - já possui totais calculados</span><br>\n";
            $skipped_count++;
            continue;
        }
        
        echo "<strong>• Processando Registro #$register_id (usuário $user_id)</strong><br>\n";
        echo "&nbsp;&nbsp;Período: " . date('d/m/Y H:i', strtotime($open_date)) . " até " . date('d/m/Y H:i', strtotime($close_date)) . "<br>\n";
        
        // Calcular total_cash (dinheiro)
        $stmt_cash = $pdo->prepare("
            SELECT SUM(COALESCE(amount, 0)) as total
            FROM tec_payments 
            WHERE date > ? 
            AND date <= ?
            AND created_by = ? 
            AND paid_by = 'cash'
        ");
        $stmt_cash->execute([$open_date, $close_date, $user_id]);
        $total_cash = $stmt_cash->fetchColumn() ?: 0;
        
        // Calcular total_stripe (débito)
        $stmt_stripe = $pdo->prepare("
            SELECT SUM(COALESCE(amount, 0)) as total
            FROM tec_payments 
            WHERE date > ? 
            AND date <= ?
            AND created_by = ? 
            AND paid_by = 'stripe'
        ");
        $stmt_stripe->execute([$open_date, $close_date, $user_id]);
        $total_stripe = $stmt_stripe->fetchColumn() ?: 0;
        
        // Calcular total_CC (crédito)
        $stmt_cc = $pdo->prepare("
            SELECT SUM(COALESCE(amount, 0)) as total
            FROM tec_payments 
            WHERE date > ? 
            AND date <= ?
            AND created_by = ? 
            AND paid_by = 'CC'
        ");
        $stmt_cc->execute([$open_date, $close_date, $user_id]);
        $total_cc = $stmt_cc->fetchColumn() ?: 0;
        
        // Calcular total_pix
        $stmt_pix = $pdo->prepare("
            SELECT SUM(COALESCE(amount, 0)) as total
            FROM tec_payments 
            WHERE date > ? 
            AND date <= ?
            AND created_by = ? 
            AND paid_by = 'pix'
        ");
        $stmt_pix->execute([$open_date, $close_date, $user_id]);
        $total_pix = $stmt_pix->fetchColumn() ?: 0;
        
        // Atualizar o registro
        $stmt_update = $pdo->prepare("
            UPDATE tec_registers 
            SET total_stripe = ?, total_CC = ?, total_pix = ?
            WHERE id = ?
        ");
        $stmt_update->execute([$total_stripe, $total_cc, $total_pix, $register_id]);
        
        echo "&nbsp;&nbsp;<span style='color: green;'>✓ Totais calculados:</span><br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;Dinheiro: R$ " . number_format($total_cash, 2, ',', '.') . "<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;Débito: R$ " . number_format($total_stripe, 2, ',', '.') . "<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;Crédito: R$ " . number_format($total_cc, 2, ',', '.') . "<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;PIX: R$ " . number_format($total_pix, 2, ',', '.') . "<br>\n";
        echo "<br>\n";
        
        $updated_count++;
        
        // Flush output para mostrar progresso em tempo real
        if (ob_get_level()) {
            ob_flush();
        }
        flush();
    }
    
    echo "</div>\n";
    echo "<br><h3>Resumo:</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Total de registros encontrados:</strong> $total_registers</li>\n";
    echo "<li><strong style='color: green;'>Registros atualizados:</strong> $updated_count</li>\n";
    echo "<li><strong style='color: #666;'>Registros já processados:</strong> $skipped_count</li>\n";
    echo "</ul>\n";
    
    if ($updated_count > 0) {
        echo "<p style='color: green; font-weight: bold;'>✓ Recálculo concluído com sucesso!</p>\n";
        echo "<p>Agora você pode verificar os relatórios de caixa para ver os totais de cada meio de pagamento.</p>\n";
    } else {
        echo "<p style='color: blue;'>ℹ Todos os registros já possuíam os totais calculados.</p>\n";
    }
    
} catch (PDOException $e) {
    echo "<strong style='color: red;'>Erro: " . $e->getMessage() . "</strong><br>\n";
    echo "Verifique as configurações do banco de dados no início deste arquivo.<br>\n";
}
?>
