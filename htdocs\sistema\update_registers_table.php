<?php
/**
 * Script para adicionar campos de meios de pagamento na tabela tec_registers
 * Execute este arquivo uma vez para atualizar a estrutura da tabela
 */

// Configurações do banco de dados - ajuste conforme necessário
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sis_nfe_pdv'; // Ajuste o nome do banco

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Atualizando estrutura da tabela tec_registers...</h2>\n";
    
    // Verificar se os campos já existem
    $fields_to_add = [];
    
    // Verificar total_stripe
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'tec_registers' AND COLUMN_NAME = 'total_stripe'");
    $stmt->execute([$database]);
    if ($stmt->fetchColumn() == 0) {
        $fields_to_add[] = "ADD COLUMN `total_stripe` decimal(25,2) DEFAULT NULL AFTER `total_cash`";
        echo "- Campo total_stripe será adicionado<br>\n";
    } else {
        echo "- Campo total_stripe já existe<br>\n";
    }
    
    // Verificar total_CC
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'tec_registers' AND COLUMN_NAME = 'total_CC'");
    $stmt->execute([$database]);
    if ($stmt->fetchColumn() == 0) {
        $fields_to_add[] = "ADD COLUMN `total_CC` decimal(25,2) DEFAULT NULL AFTER `total_stripe`";
        echo "- Campo total_CC será adicionado<br>\n";
    } else {
        echo "- Campo total_CC já existe<br>\n";
    }
    
    // Verificar total_pix
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'tec_registers' AND COLUMN_NAME = 'total_pix'");
    $stmt->execute([$database]);
    if ($stmt->fetchColumn() == 0) {
        $fields_to_add[] = "ADD COLUMN `total_pix` decimal(25,2) DEFAULT NULL AFTER `total_CC`";
        echo "- Campo total_pix será adicionado<br>\n";
    } else {
        echo "- Campo total_pix já existe<br>\n";
    }
    
    // Executar ALTER TABLE se houver campos para adicionar
    if (!empty($fields_to_add)) {
        $sql = "ALTER TABLE `tec_registers` " . implode(', ', $fields_to_add);
        echo "<br>Executando: <code>$sql</code><br><br>\n";
        
        $pdo->exec($sql);
        echo "<strong style='color: green;'>✓ Campos adicionados com sucesso!</strong><br>\n";
    } else {
        echo "<br><strong style='color: blue;'>ℹ Todos os campos já existem na tabela.</strong><br>\n";
    }
    
    echo "<br><h3>Estrutura atual da tabela tec_registers:</h3>\n";
    $stmt = $pdo->query("DESCRIBE tec_registers");
    echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr>\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
} catch (PDOException $e) {
    echo "<strong style='color: red;'>Erro: " . $e->getMessage() . "</strong><br>\n";
    echo "Verifique as configurações do banco de dados no início deste arquivo.<br>\n";
}
?>
