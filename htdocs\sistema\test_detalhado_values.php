<?php
// Teste para verificar os valores de grand_total na página detalhado
require_once 'app/config/database.php';

// Conectar ao banco
$host = $db['default']['hostname'];
$user = $db['default']['username'];
$pass = $db['default']['password'];
$database = $db['default']['database'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Teste de Valores - Vendas Detalhadas</h2>";
    
    // Buscar algumas vendas recentes
    $stmt = $pdo->prepare("
        SELECT 
            s.id, 
            s.date, 
            s.grand_total, 
            s.customer_name,
            u.first_name,
            u.last_name
        FROM sales s
        LEFT JOIN users u ON s.created_by = u.id
        ORDER BY s.id DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $vendas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($vendas) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
        echo "<tr style='background: #f0f0f0;'>\n";
        echo "<th>ID</th><th>Data</th><th>Cliente</th><th>Usuário</th><th>Grand Total (Raw)</th><th>Grand Total (Formatado)</th>\n";
        echo "</tr>\n";
        
        // Incluir a biblioteca Tec para formatação
        require_once 'app/libraries/Tec.php';
        $tec = new Tec();
        
        foreach ($vendas as $venda) {
            echo "<tr>\n";
            echo "<td>" . htmlspecialchars($venda['id']) . "</td>\n";
            echo "<td>" . htmlspecialchars($venda['date']) . "</td>\n";
            echo "<td>" . htmlspecialchars($venda['customer_name']) . "</td>\n";
            echo "<td>" . htmlspecialchars($venda['first_name'] . ' ' . $venda['last_name']) . "</td>\n";
            echo "<td>" . htmlspecialchars($venda['grand_total']) . "</td>\n";
            echo "<td>" . htmlspecialchars($tec->formatMoney($venda['grand_total'])) . "</td>\n";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
        
        // Testar alguns valores específicos
        echo "<h3>Teste de Formatação</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr style='background: #f0f0f0;'>\n";
        echo "<th>Valor Original</th><th>Formatado</th>\n";
        echo "</tr>\n";
        
        $test_values = [31378, 1234.56, 999.99, 10000, 313.78, 0, 0.50, 1.23];
        
        foreach ($test_values as $value) {
            echo "<tr>\n";
            echo "<td>" . $value . "</td>\n";
            echo "<td>" . $tec->formatMoney($value) . "</td>\n";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
        
    } else {
        echo "<p>Nenhuma venda encontrada.</p>";
    }
    
} catch (PDOException $e) {
    echo "Erro de conexão: " . $e->getMessage();
}
?>
